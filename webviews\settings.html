<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <script type="module" crossorigin src="./assets/settings-Bk3inREu.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BejxPZX4.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DmfIVAYG.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-D7f6isRQ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-B_Xbv83k.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-B4wu8Zer.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-dQY7gB6x.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-B8Zm7GJJ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-LNVO_dOZ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-PxcJW8dJ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/download-BzI84HIO.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Cz4_d9qQ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-ChMRIfwW.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-Dkoz80k5.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BRv5sK55.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-Bum6OuhB.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CVatBELe.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-DH8_XiUe.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-5PNRCAQu.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DCLSWWW0.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/copy-BA1J_YQn.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-03DKN6zM.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-D78W1-g3.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BlaqVU85.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-6AlVWKh5.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-Dxj8rwt5.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/rules-model-CvhsIRLH.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-DJARyuBd.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-DBFrW_GB.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-VPe0cp57.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/download-Bd_7IRvZ.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-lVYABfHh.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-FeoFGSYm.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/partner-mcp-utils-BRhZ8F7l.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/index-CPuOq1ei.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-DwFbLE28.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-B23ZKhTC.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-pxiddGnV.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-DFdffRNP.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/rules-model-B6vv3aGc.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BzgVnrJx.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/settings-sJv-jASV.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>

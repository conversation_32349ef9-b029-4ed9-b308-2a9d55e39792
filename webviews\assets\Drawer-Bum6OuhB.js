import{S as U,i as Y,s as Z,J as q,d as O,P as ee,t as L,q as x,K as E,L as F,M as G,Q as B,a1 as g,o as te,p as se,h as z,c as P,e as y,R as D,ag as ae,F as _,V as X,ah as de,D as V,ai as S,aj as oe,E as C,G as H,a3 as I,aa as ne}from"./SpinnerAugment-BejxPZX4.js";import{I as le}from"./IconButtonAugment-DmfIVAYG.js";import{f as J}from"./index-Dkoz80k5.js";import{E as re}from"./ellipsis-BRv5sK55.js";const ie=(s,{onResize:t,options:e})=>{const d=new ResizeObserver(t);return d.observe(s,e),{destroy(){d.unobserve(s),d.disconnect()}}},ce=s=>({}),K=s=>({}),ue=s=>({}),Q=s=>({});function A(s){let t,e,d,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:s}}}),e.$on("click",s[12]),{c(){t=_("div"),H(e.$$.fragment),z(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){P(l,t,$),C(e,t,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(x(e.$$.fragment,l),l&&oe(()=>{u&&(d||(d=S(t,J,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(l){L(e.$$.fragment,l),l&&(d||(d=S(t,J,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(l){l&&O(t),V(e),l&&d&&d.end()}}}function me(s){let t,e;return t=new re({}),{c(){H(t.$$.fragment)},m(d,u){C(t,d,u),e=!0},i(d){e||(x(t.$$.fragment,d),e=!0)},o(d){L(t.$$.fragment,d),e=!1},d(d){V(t,d)}}}function he(s){let t,e,d,u,l,$,p,b,v,f,i,m,k;const M=s[20].left,h=q(M,s,s[24],Q),W=s[20].right,r=q(W,s,s[24],K);let n=s[0]&&s[3]&&A(s);return{c(){t=_("div"),e=_("div"),d=_("div"),h&&h.c(),u=X(),l=_("div"),$=X(),p=_("div"),r&&r.c(),b=X(),n&&n.c(),z(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=s[7],B(d,"width","var(--augment-drawer-width)"),B(d,"min-width","var(--augment-drawer-width)"),B(d,"max-width","var(--augment-drawer-width)"),z(e,"class","c-drawer__left svelte-18f0m3o"),B(e,"--augment-drawer-width",s[8]+"px"),z(l,"aria-hidden","true"),z(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",s[4]),z(p,"class","c-drawer__right svelte-18f0m3o"),z(t,"class",v="c-drawer "+s[2]+" svelte-18f0m3o"),g(t,"is-dragging",s[7]),g(t,"is-hidden",!s[8]),g(t,"is-column",s[4])},m(a,c){P(a,t,c),y(t,e),y(e,d),h&&h.m(d,null),s[21](e),y(t,u),y(t,l),y(t,$),y(t,p),r&&r.m(p,null),y(t,b),n&&n.m(t,null),s[22](t),i=!0,m||(k=[D(window,"mousemove",s[10]),D(window,"mouseup",s[11]),D(l,"mousedown",s[9]),D(l,"dblclick",s[12]),ae(f=ie.call(null,t,{onResize:s[23]}))],m=!0)},p(a,[c]){h&&h.p&&(!i||16777216&c)&&E(h,M,a,a[24],i?G(M,a[24],c,ue):F(a[24]),Q),(!i||128&c)&&(d.inert=a[7]),(!i||256&c)&&B(e,"--augment-drawer-width",a[8]+"px"),(!i||16&c)&&g(l,"is-locked",a[4]),r&&r.p&&(!i||16777216&c)&&E(r,W,a,a[24],i?G(W,a[24],c,ce):F(a[24]),K),a[0]&&a[3]?n?(n.p(a,c),9&c&&x(n,1)):(n=A(a),n.c(),x(n,1),n.m(t,null)):n&&(te(),L(n,1,1,()=>{n=null}),se()),(!i||4&c&&v!==(v="c-drawer "+a[2]+" svelte-18f0m3o"))&&z(t,"class",v),f&&ne(f.update)&&2&c&&f.update.call(null,{onResize:a[23]}),(!i||132&c)&&g(t,"is-dragging",a[7]),(!i||260&c)&&g(t,"is-hidden",!a[8]),(!i||20&c)&&g(t,"is-column",a[4])},i(a){i||(x(h,a),x(r,a),x(n),i=!0)},o(a){L(h,a),L(r,a),L(n),i=!1},d(a){a&&O(t),h&&h.d(a),s[21](null),r&&r.d(a),n&&n.d(),s[22](null),m=!1,ee(k)}}}function fe(s,t,e){let d,u,l,$,{$$slots:p={},$$scope:b}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:k=""}=t,{showButton:M=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:W=600}=t,{layoutMode:r}=t,n=!1,a=v,c=v,w=!1;function R(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<W),w&&e(7,n=!1)}}return de(R),s.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,k=o.class),"showButton"in o&&e(3,M=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,W=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,b=o.$$scope)},s.$$.update=()=>{3&s.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),R())),18&s.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&s.$$.dirty&&e(8,c=m?i:a)},[m,r,k,M,w,d,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const N=o.clientX-l,j=u.clientWidth-200,T=$+N;T<f?T<f-h?e(0,m=!0):(e(19,a=f),e(0,m=!1)):T>j?(e(19,a=j),e(0,m=!1)):(e(19,a=T),e(0,m=!1))},function(){e(7,n=!1),e(19,a=Math.max(a,f))},function(){e(0,m=!m)},R,v,f,i,h,W,a,p,function(o){I[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){I[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&R(),b]}class ge extends U{constructor(t){super(),Y(this,t,fe,he,Z,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{ge as D,ie as r};

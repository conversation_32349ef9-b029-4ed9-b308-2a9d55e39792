import{_ as h,F as et,d as R,e as ie,l as w,x as oe,B as le,c as z,aj as ce,a1 as de,a8 as he,ak as q,al as Pt,am as ge,u as V,k as pe,an as ye,ao as gt,ap as ue,i as Dt}from"./AugmentMessage-bvgL2GJ0.js";import{c as xe}from"./clone-C4dhdsvk.js";import{G as be}from"./graph-KsABUsDv.js";import{c as fe}from"./channel-C8ZVVkcE.js";import"./SpinnerAugment-BejxPZX4.js";import"./IconButtonAugment-DmfIVAYG.js";import"./CalloutAugment-DH8_XiUe.js";import"./CardAugment-dQY7gB6x.js";import"./index-LNVO_dOZ.js";import"./async-messaging-D7f6isRQ.js";import"./message-broker-B_Xbv83k.js";import"./types-CGlLNakm.js";import"./file-paths-B4wu8Zer.js";import"./BaseTextInput-B8Zm7GJJ.js";import"./folder-opened-ChvTYBfK.js";import"./index-BlaqVU85.js";import"./diff-operations-BLNMYUZC.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-DSk2r097.js";import"./index-6AlVWKh5.js";import"./keypress-DD1aQVr0.js";import"./await_block-Dt1rTGTc.js";import"./OpenFileButton-DltEK_xL.js";import"./chat-context-CmoAn9pE.js";import"./index-B528snJk.js";import"./remote-agents-client-DwCOg82s.js";import"./ra-diff-ops-model-DHtiwZCr.js";import"./TextAreaAugment-DCLSWWW0.js";import"./ButtonAugment-CVatBELe.js";import"./CollapseButtonAugment-03DKN6zM.js";import"./partner-mcp-utils-PxcJW8dJ.js";import"./MaterialIcon-CUEAtZVx.js";import"./CopyButton-Cb58npax.js";import"./copy-BA1J_YQn.js";import"./ellipsis-BRv5sK55.js";import"./IconFilePath-BUMTFdYT.js";import"./LanguageIcon-B9tL-FlO.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-DMucGkIH.js";import"./index-Dkoz80k5.js";import"./augment-logo-BRK9m6wM.js";import"./pen-to-square-5PNRCAQu.js";import"./chevron-down-D78W1-g3.js";import"./check-Cc9E51ln.js";import"./_baseUniq-Dec7a2Hj.js";var pt=function(){var e=h(function(d,u,k,b){for(k=k||{},b=d.length;b--;k[d[b]]=u);return k},"o"),t=[1,7],a=[1,13],n=[1,14],r=[1,15],s=[1,19],i=[1,16],o=[1,17],c=[1,18],y=[8,30],p=[8,21,28,29,30,31,32,40,44,47],x=[1,23],f=[1,24],_=[8,15,16,21,28,29,30,31,32,40,44,47],S=[8,15,16,21,27,28,29,30,31,32,40,44,47],E=[1,49],D={trace:h(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:h(function(d,u,k,b,m,l,L){var g=l.length-1;switch(m){case 4:b.getLogger().debug("Rule: separator (NL) ");break;case 5:b.getLogger().debug("Rule: separator (Space) ");break;case 6:b.getLogger().debug("Rule: separator (EOF) ");break;case 7:b.getLogger().debug("Rule: hierarchy: ",l[g-1]),b.setHierarchy(l[g-1]);break;case 8:b.getLogger().debug("Stop NL ");break;case 9:b.getLogger().debug("Stop EOF ");break;case 10:b.getLogger().debug("Stop NL2 ");break;case 11:b.getLogger().debug("Stop EOF2 ");break;case 12:b.getLogger().debug("Rule: statement: ",l[g]),typeof l[g].length=="number"?this.$=l[g]:this.$=[l[g]];break;case 13:b.getLogger().debug("Rule: statement #2: ",l[g-1]),this.$=[l[g-1]].concat(l[g]);break;case 14:b.getLogger().debug("Rule: link: ",l[g],d),this.$={edgeTypeStr:l[g],label:""};break;case 15:b.getLogger().debug("Rule: LABEL link: ",l[g-3],l[g-1],l[g]),this.$={edgeTypeStr:l[g],label:l[g-1]};break;case 18:const $=parseInt(l[g]),H=b.generateId();this.$={id:H,type:"space",label:"",width:$,children:[]};break;case 23:b.getLogger().debug("Rule: (nodeStatement link node) ",l[g-2],l[g-1],l[g]," typestr: ",l[g-1].edgeTypeStr);const U=b.edgeStrToEdgeData(l[g-1].edgeTypeStr);this.$=[{id:l[g-2].id,label:l[g-2].label,type:l[g-2].type,directions:l[g-2].directions},{id:l[g-2].id+"-"+l[g].id,start:l[g-2].id,end:l[g].id,label:l[g-1].label,type:"edge",directions:l[g].directions,arrowTypeEnd:U,arrowTypeStart:"arrow_open"},{id:l[g].id,label:l[g].label,type:b.typeStr2Type(l[g].typeStr),directions:l[g].directions}];break;case 24:b.getLogger().debug("Rule: nodeStatement (abc88 node size) ",l[g-1],l[g]),this.$={id:l[g-1].id,label:l[g-1].label,type:b.typeStr2Type(l[g-1].typeStr),directions:l[g-1].directions,widthInColumns:parseInt(l[g],10)};break;case 25:b.getLogger().debug("Rule: nodeStatement (node) ",l[g]),this.$={id:l[g].id,label:l[g].label,type:b.typeStr2Type(l[g].typeStr),directions:l[g].directions,widthInColumns:1};break;case 26:b.getLogger().debug("APA123",this?this:"na"),b.getLogger().debug("COLUMNS: ",l[g]),this.$={type:"column-setting",columns:l[g]==="auto"?-1:parseInt(l[g])};break;case 27:b.getLogger().debug("Rule: id-block statement : ",l[g-2],l[g-1]),b.generateId(),this.$={...l[g-2],type:"composite",children:l[g-1]};break;case 28:b.getLogger().debug("Rule: blockStatement : ",l[g-2],l[g-1],l[g]);const T=b.generateId();this.$={id:T,type:"composite",label:"",children:l[g-1]};break;case 29:b.getLogger().debug("Rule: node (NODE_ID separator): ",l[g]),this.$={id:l[g]};break;case 30:b.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",l[g-1],l[g]),this.$={id:l[g-1],label:l[g].label,typeStr:l[g].typeStr,directions:l[g].directions};break;case 31:b.getLogger().debug("Rule: dirList: ",l[g]),this.$=[l[g]];break;case 32:b.getLogger().debug("Rule: dirList: ",l[g-1],l[g]),this.$=[l[g-1]].concat(l[g]);break;case 33:b.getLogger().debug("Rule: nodeShapeNLabel: ",l[g-2],l[g-1],l[g]),this.$={typeStr:l[g-2]+l[g],label:l[g-1]};break;case 34:b.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",l[g-3],l[g-2]," #3:",l[g-1],l[g]),this.$={typeStr:l[g-3]+l[g],label:l[g-2],directions:l[g-1]};break;case 35:case 36:this.$={type:"classDef",id:l[g-1].trim(),css:l[g].trim()};break;case 37:this.$={type:"applyClass",id:l[g-1].trim(),styleClass:l[g].trim()};break;case 38:this.$={type:"applyStyles",id:l[g-1].trim(),stylesStr:l[g].trim()}}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:n,31:r,32:s,40:i,44:o,47:c},{8:[1,20]},e(y,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:a,29:n,31:r,32:s,40:i,44:o,47:c}),e(p,[2,16],{14:22,15:x,16:f}),e(p,[2,17]),e(p,[2,18]),e(p,[2,19]),e(p,[2,20]),e(p,[2,21]),e(p,[2,22]),e(_,[2,25],{27:[1,25]}),e(p,[2,26]),{19:26,26:12,32:s},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:n,31:r,32:s,40:i,44:o,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(S,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(y,[2,13]),{26:35,32:s},{32:[2,14]},{17:[1,36]},e(_,[2,24]),{11:37,13:4,14:22,15:x,16:f,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:n,31:r,32:s,40:i,44:o,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(S,[2,30]),{18:[1,43]},{18:[1,44]},e(_,[2,23]),{18:[1,45]},{30:[1,46]},e(p,[2,28]),e(p,[2,35]),e(p,[2,36]),e(p,[2,37]),e(p,[2,38]),{37:[1,47]},{34:48,35:E},{15:[1,50]},e(p,[2,27]),e(S,[2,33]),{39:[1,51]},{34:52,35:E,39:[2,31]},{32:[2,15]},e(S,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:h(function(d,u){if(!u.recoverable){var k=new Error(d);throw k.hash=u,k}this.trace(d)},"parseError"),parse:h(function(d){var u=this,k=[0],b=[],m=[null],l=[],L=this.table,g="",$=0,H=0,U=l.slice.call(arguments,1),T=Object.create(this.lexer),M={yy:{}};for(var dt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,dt)&&(M.yy[dt]=this.yy[dt]);T.setInput(d,M.yy),M.yy.lexer=T,M.yy.parser=this,T.yylloc===void 0&&(T.yylloc={});var ht=T.yylloc;l.push(ht);var ne=T.options&&T.options.ranges;function Lt(){var X;return typeof(X=b.pop()||T.lex()||1)!="number"&&(X instanceof Array&&(X=(b=X).pop()),X=u.symbols_[X]||X),X}typeof M.yy.parseError=="function"?this.parseError=M.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,h(function(X){k.length=k.length-2*X,m.length=m.length-X,l.length=l.length-X},"popStack"),h(Lt,"lex");for(var P,G,W,_t,rt,Z,Et,at,J={};;){if(G=k[k.length-1],this.defaultActions[G]?W=this.defaultActions[G]:(P==null&&(P=Lt()),W=L[G]&&L[G][P]),W===void 0||!W.length||!W[0]){var vt="";for(rt in at=[],L[G])this.terminals_[rt]&&rt>2&&at.push("'"+this.terminals_[rt]+"'");vt=T.showPosition?"Parse error on line "+($+1)+`:
`+T.showPosition()+`
Expecting `+at.join(", ")+", got '"+(this.terminals_[P]||P)+"'":"Parse error on line "+($+1)+": Unexpected "+(P==1?"end of input":"'"+(this.terminals_[P]||P)+"'"),this.parseError(vt,{text:T.match,token:this.terminals_[P]||P,line:T.yylineno,loc:ht,expected:at})}if(W[0]instanceof Array&&W.length>1)throw new Error("Parse Error: multiple actions possible at state: "+G+", token: "+P);switch(W[0]){case 1:k.push(P),m.push(T.yytext),l.push(T.yylloc),k.push(W[1]),P=null,H=T.yyleng,g=T.yytext,$=T.yylineno,ht=T.yylloc;break;case 2:if(Z=this.productions_[W[1]][1],J.$=m[m.length-Z],J._$={first_line:l[l.length-(Z||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(Z||1)].first_column,last_column:l[l.length-1].last_column},ne&&(J._$.range=[l[l.length-(Z||1)].range[0],l[l.length-1].range[1]]),(_t=this.performAction.apply(J,[g,H,$,M.yy,W[1],m,l].concat(U)))!==void 0)return _t;Z&&(k=k.slice(0,-1*Z*2),m=m.slice(0,-1*Z),l=l.slice(0,-1*Z)),k.push(this.productions_[W[1]][0]),m.push(J.$),l.push(J._$),Et=L[k[k.length-2]][k[k.length-1]],k.push(Et);break;case 3:return!0}}return!0},"parse")},N=function(){return{EOF:1,parseError:h(function(d,u){if(!this.yy.parser)throw new Error(d);this.yy.parser.parseError(d,u)},"parseError"),setInput:h(function(d,u){return this.yy=u||this.yy||{},this._input=d,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:h(function(){var d=this._input[0];return this.yytext+=d,this.yyleng++,this.offset++,this.match+=d,this.matched+=d,d.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),d},"input"),unput:h(function(d){var u=d.length,k=d.split(/(?:\r\n?|\n)/g);this._input=d+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-u),this.offset-=u;var b=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),k.length-1&&(this.yylineno-=k.length-1);var m=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:k?(k.length===b.length?this.yylloc.first_column:0)+b[b.length-k.length].length-k[0].length:this.yylloc.first_column-u},this.options.ranges&&(this.yylloc.range=[m[0],m[0]+this.yyleng-u]),this.yyleng=this.yytext.length,this},"unput"),more:h(function(){return this._more=!0,this},"more"),reject:h(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:h(function(d){this.unput(this.match.slice(d))},"less"),pastInput:h(function(){var d=this.matched.substr(0,this.matched.length-this.match.length);return(d.length>20?"...":"")+d.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:h(function(){var d=this.match;return d.length<20&&(d+=this._input.substr(0,20-d.length)),(d.substr(0,20)+(d.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:h(function(){var d=this.pastInput(),u=new Array(d.length+1).join("-");return d+this.upcomingInput()+`
`+u+"^"},"showPosition"),test_match:h(function(d,u){var k,b,m;if(this.options.backtrack_lexer&&(m={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(m.yylloc.range=this.yylloc.range.slice(0))),(b=d[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=b.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:b?b[b.length-1].length-b[b.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+d[0].length},this.yytext+=d[0],this.match+=d[0],this.matches=d,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(d[0].length),this.matched+=d[0],k=this.performAction.call(this,this.yy,this,u,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),k)return k;if(this._backtrack){for(var l in m)this[l]=m[l];return!1}return!1},"test_match"),next:h(function(){if(this.done)return this.EOF;var d,u,k,b;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var m=this._currentRules(),l=0;l<m.length;l++)if((k=this._input.match(this.rules[m[l]]))&&(!u||k[0].length>u[0].length)){if(u=k,b=l,this.options.backtrack_lexer){if((d=this.test_match(k,m[l]))!==!1)return d;if(this._backtrack){u=!1;continue}return!1}if(!this.options.flex)break}return u?(d=this.test_match(u,m[b]))!==!1&&d:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:h(function(){var d=this.next();return d||this.lex()},"lex"),begin:h(function(d){this.conditionStack.push(d)},"begin"),popState:h(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:h(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:h(function(d){return(d=this.conditionStack.length-1-Math.abs(d||0))>=0?this.conditionStack[d]:"INITIAL"},"topState"),pushState:h(function(d){this.begin(d)},"pushState"),stateStackSize:h(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:h(function(d,u,k,b){switch(k){case 0:return 10;case 1:return d.getLogger().debug("Found space-block"),31;case 2:return d.getLogger().debug("Found nl-block"),31;case 3:return d.getLogger().debug("Found space-block"),29;case 4:d.getLogger().debug(".",u.yytext);break;case 5:d.getLogger().debug("_",u.yytext);break;case 6:return 5;case 7:return u.yytext=-1,28;case 8:return u.yytext=u.yytext.replace(/columns\s+/,""),d.getLogger().debug("COLUMNS (LEX)",u.yytext),28;case 9:case 77:case 78:case 100:this.pushState("md_string");break;case 10:return"MD_STR";case 11:case 35:case 80:this.popState();break;case 12:this.pushState("string");break;case 13:d.getLogger().debug("LEX: POPPING STR:",u.yytext),this.popState();break;case 14:return d.getLogger().debug("LEX: STR end:",u.yytext),"STR";case 15:return u.yytext=u.yytext.replace(/space\:/,""),d.getLogger().debug("SPACE NUM (LEX)",u.yytext),21;case 16:return u.yytext="1",d.getLogger().debug("COLUMNS (LEX)",u.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:case 39:case 41:case 42:case 45:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),d.getLogger().debug("Lex: ))"),"NODE_DEND";case 43:return this.popState(),d.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),d.getLogger().debug("Lex: -)"),"NODE_DEND";case 46:return this.popState(),d.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),d.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),d.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:case 50:return this.popState(),d.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),d.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),d.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),d.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),d.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return d.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return d.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return d.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:case 60:case 61:case 62:case 65:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return d.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 63:return d.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return d.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 66:case 67:case 68:case 69:case 70:case 71:case 72:return this.pushState("NODE"),36;case 73:return d.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),d.getLogger().debug("LEX ARR START"),38;case 75:return d.getLogger().debug("Lex: NODE_ID",u.yytext),32;case 76:return d.getLogger().debug("Lex: EOF",u.yytext),8;case 79:return"NODE_DESCR";case 81:d.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:d.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return d.getLogger().debug("LEX: NODE_DESCR:",u.yytext),"NODE_DESCR";case 84:d.getLogger().debug("LEX POPPING"),this.popState();break;case 85:d.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (right): dir:",u.yytext),"DIR";case 87:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (left):",u.yytext),"DIR";case 88:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (x):",u.yytext),"DIR";case 89:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (y):",u.yytext),"DIR";case 90:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (up):",u.yytext),"DIR";case 91:return u.yytext=u.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (down):",u.yytext),"DIR";case 92:return u.yytext="]>",d.getLogger().debug("Lex (ARROW_DIR end):",u.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return d.getLogger().debug("Lex: LINK","#"+u.yytext+"#"),15;case 94:case 95:case 96:return d.getLogger().debug("Lex: LINK",u.yytext),15;case 97:case 98:case 99:return d.getLogger().debug("Lex: START_LINK",u.yytext),this.pushState("LLABEL"),16;case 101:return d.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),d.getLogger().debug("Lex: LINK","#"+u.yytext+"#"),15;case 103:case 104:return this.popState(),d.getLogger().debug("Lex: LINK",u.yytext),15;case 105:return d.getLogger().debug("Lex: COLON",u.yytext),u.yytext=u.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}}}();function v(){this.yy={}}return D.lexer=N,h(v,"Parser"),v.prototype=D,D.Parser=v,new v}();pt.parser=pt;var me=pt,K=new Map,ft=[],yt=new Map,$t="color",Tt="fill",we=z(),it=new Map,ke=h(e=>pe.sanitizeText(e,we),"sanitizeText"),Se=h(function(e,t=""){let a=it.get(e);a||(a={id:e,styles:[],textStyles:[]},it.set(e,a)),t!=null&&t.split(",").forEach(n=>{const r=n.replace(/([^;]*);/,"$1").trim();if(RegExp($t).exec(n)){const s=r.replace(Tt,"bgFill").replace($t,Tt);a.textStyles.push(s)}a.styles.push(r)})},"addStyleClass"),Le=h(function(e,t=""){const a=K.get(e);t!=null&&(a.styles=t.split(","))},"addStyle2Node"),_e=h(function(e,t){e.split(",").forEach(function(a){let n=K.get(a);if(n===void 0){const r=a.trim();n={id:r,type:"na",children:[]},K.set(r,n)}n.classes||(n.classes=[]),n.classes.push(t)})},"setCssClass"),Yt=h((e,t)=>{const a=e.flat(),n=[];for(const r of a)if(r.label&&(r.label=ke(r.label)),r.type!=="classDef")if(r.type!=="applyClass")if(r.type!=="applyStyles")if(r.type==="column-setting")t.columns=r.columns??-1;else if(r.type==="edge"){const s=(yt.get(r.id)??0)+1;yt.set(r.id,s),r.id=s+"-"+r.id,ft.push(r)}else{r.label||(r.type==="composite"?r.label="":r.label=r.id);const s=K.get(r.id);if(s===void 0?K.set(r.id,r):(r.type!=="na"&&(s.type=r.type),r.label!==r.id&&(s.label=r.label)),r.children&&Yt(r.children,r),r.type==="space"){const i=r.width??1;for(let o=0;o<i;o++){const c=xe(r);c.id=c.id+"-"+o,K.set(c.id,c),n.push(c)}}else s===void 0&&n.push(r)}else r!=null&&r.stylesStr&&Le(r.id,r==null?void 0:r.stylesStr);else _e(r.id,(r==null?void 0:r.styleClass)??"");else Se(r.id,r.css);t.children=n},"populateBlockDatabase"),mt=[],tt={id:"root",type:"composite",children:[],columns:-1},Ee=h(()=>{w.debug("Clear called"),oe(),tt={id:"root",type:"composite",children:[],columns:-1},K=new Map([["root",tt]]),mt=[],it=new Map,ft=[],yt=new Map},"clear");function Wt(e){switch(w.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return w.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}function Xt(e){return w.debug("typeStr2Type",e),e==="=="?"thick":"normal"}function Ft(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}h(Wt,"typeStr2Type"),h(Xt,"edgeTypeStr2Type"),h(Ft,"edgeStrToEdgeData");var Ct=0,ve=h(()=>(Ct++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Ct),"generateId"),De=h(e=>{tt.children=e,Yt(e,tt),mt=tt.children},"setHierarchy"),$e=h(e=>{const t=K.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Te=h(()=>[...K.values()],"getBlocksFlat"),Ce=h(()=>mt||[],"getBlocks"),Ne=h(()=>ft,"getEdges"),Ie=h(e=>K.get(e),"getBlock"),Be=h(e=>{K.set(e.id,e)},"setBlock"),ze=h(()=>console,"getLogger"),Re=h(function(){return it},"getClasses"),Oe={getConfig:h(()=>et().block,"getConfig"),typeStr2Type:Wt,edgeTypeStr2Type:Xt,edgeStrToEdgeData:Ft,getLogger:ze,getBlocksFlat:Te,getBlocks:Ce,getEdges:Ne,setHierarchy:De,getBlock:Ie,setBlock:Be,getColumns:$e,getClasses:Re,clear:Ee,generateId:ve},st=h((e,t)=>{const a=fe,n=a(e,"r"),r=a(e,"g"),s=a(e,"b");return le(n,r,s,t)},"fade"),Ae=h(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${st(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${st(e.mainBkg,.5)};
    fill: ${st(e.clusterBkg,.5)};
    stroke: ${st(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),Me=h((e,t,a,n)=>{t.forEach(r=>{Pe[r](e,a,n)})},"insertMarkers"),Pe={extension:h((e,t,a)=>{w.trace("Making markers for ",a),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),composition:h((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),aggregation:h((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),dependency:h((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),lollipop:h((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),point:h((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),circle:h((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),cross:h((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),barb:h((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb")},Ye=Me,At,Mt,B=((Mt=(At=z())==null?void 0:At.block)==null?void 0:Mt.padding)??8;function Ht(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);return e<0?{px:t,py:0}:e===1?{px:0,py:t}:{px:t%e,py:Math.floor(t/e)}}h(Ht,"calculateBlockPosition");var We=h(e=>{let t=0,a=0;for(const n of e.children){const{width:r,height:s,x:i,y:o}=n.size??{width:0,height:0,x:0,y:0};w.debug("getMaxChildSize abc95 child:",n.id,"width:",r,"height:",s,"x:",i,"y:",o,n.type),n.type!=="space"&&(r>t&&(t=r/(e.widthInColumns??1)),s>a&&(a=s))}return{width:t,height:a}},"getMaxChildSize");function ot(e,t,a=0,n=0){var i,o,c,y,p,x,f,_,S,E,D;w.debug("setBlockSizes abc95 (start)",e.id,(i=e==null?void 0:e.size)==null?void 0:i.x,"block width =",e==null?void 0:e.size,"sieblingWidth",a),(o=e==null?void 0:e.size)!=null&&o.width||(e.size={width:a,height:n,x:0,y:0});let r=0,s=0;if(((c=e.children)==null?void 0:c.length)>0){for(const l of e.children)ot(l,t);const N=We(e);r=N.width,s=N.height,w.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",r,s);for(const l of e.children)l.size&&(w.debug(`abc95 Setting size of children of ${e.id} id=${l.id} ${r} ${s} ${JSON.stringify(l.size)}`),l.size.width=r*(l.widthInColumns??1)+B*((l.widthInColumns??1)-1),l.size.height=s,l.size.x=0,l.size.y=0,w.debug(`abc95 updating size of ${e.id} children child:${l.id} maxWidth:${r} maxHeight:${s}`));for(const l of e.children)ot(l,t,r,s);const v=e.columns??-1;let d=0;for(const l of e.children)d+=l.widthInColumns??1;let u=e.children.length;v>0&&v<d&&(u=v);const k=Math.ceil(d/u);let b=u*(r+B)+B,m=k*(s+B)+B;if(b<a){w.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${a} sieblingHeight ${n} width ${b}`),b=a,m=n;const l=(a-u*B-B)/u,L=(n-k*B-B)/k;w.debug("Size indata abc88",e.id,"childWidth",l,"maxWidth",r),w.debug("Size indata abc88",e.id,"childHeight",L,"maxHeight",s),w.debug("Size indata abc88 xSize",u,"padding",B);for(const g of e.children)g.size&&(g.size.width=l,g.size.height=L,g.size.x=0,g.size.y=0)}if(w.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${k} columns ${v}${e.children.length} width=${Math.max(b,((y=e.size)==null?void 0:y.width)||0)}`),b<(((p=e==null?void 0:e.size)==null?void 0:p.width)||0)){b=((x=e==null?void 0:e.size)==null?void 0:x.width)||0;const l=v>0?Math.min(e.children.length,v):e.children.length;if(l>0){const L=(b-l*B-B)/l;w.debug("abc95 (growing to fit) width",e.id,b,(f=e.size)==null?void 0:f.width,L);for(const g of e.children)g.size&&(g.size.width=L)}}e.size={width:b,height:m,x:0,y:0}}w.debug("setBlockSizes abc94 (done)",e.id,(_=e==null?void 0:e.size)==null?void 0:_.x,(S=e==null?void 0:e.size)==null?void 0:S.width,(E=e==null?void 0:e.size)==null?void 0:E.y,(D=e==null?void 0:e.size)==null?void 0:D.height)}function wt(e,t){var n,r,s,i,o,c,y,p,x,f,_,S,E,D,N,v,d;w.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${(n=e==null?void 0:e.size)==null?void 0:n.x} y: ${(r=e==null?void 0:e.size)==null?void 0:r.y} width: ${(s=e==null?void 0:e.size)==null?void 0:s.width}`);const a=e.columns??-1;if(w.debug("layoutBlocks columns abc95",e.id,"=>",a,e),e.children&&e.children.length>0){const u=((o=(i=e==null?void 0:e.children[0])==null?void 0:i.size)==null?void 0:o.width)??0,k=e.children.length*u+(e.children.length-1)*B;w.debug("widthOfChildren 88",k,"posX");let b=0;w.debug("abc91 block?.size?.x",e.id,(c=e==null?void 0:e.size)==null?void 0:c.x);let m=(y=e==null?void 0:e.size)!=null&&y.x?((p=e==null?void 0:e.size)==null?void 0:p.x)+(-((x=e==null?void 0:e.size)==null?void 0:x.width)/2||0):-B,l=0;for(const L of e.children){const g=e;if(!L.size)continue;const{width:$,height:H}=L.size,{px:U,py:T}=Ht(a,b);if(T!=l&&(l=T,m=(f=e==null?void 0:e.size)!=null&&f.x?((_=e==null?void 0:e.size)==null?void 0:_.x)+(-((S=e==null?void 0:e.size)==null?void 0:S.width)/2||0):-B,w.debug("New row in layout for block",e.id," and child ",L.id,l)),w.debug(`abc89 layout blocks (child) id: ${L.id} Pos: ${b} (px, py) ${U},${T} (${(E=g==null?void 0:g.size)==null?void 0:E.x},${(D=g==null?void 0:g.size)==null?void 0:D.y}) parent: ${g.id} width: ${$}${B}`),g.size){const M=$/2;L.size.x=m+B+M,w.debug(`abc91 layout blocks (calc) px, pyid:${L.id} startingPos=X${m} new startingPosX${L.size.x} ${M} padding=${B} width=${$} halfWidth=${M} => x:${L.size.x} y:${L.size.y} ${L.widthInColumns} (width * (child?.w || 1)) / 2 ${$*((L==null?void 0:L.widthInColumns)??1)/2}`),m=L.size.x+M,L.size.y=g.size.y-g.size.height/2+T*(H+B)+H/2+B,w.debug(`abc88 layout blocks (calc) px, pyid:${L.id}startingPosX${m}${B}${M}=>x:${L.size.x}y:${L.size.y}${L.widthInColumns}(width * (child?.w || 1)) / 2${$*((L==null?void 0:L.widthInColumns)??1)/2}`)}L.children&&wt(L),b+=(L==null?void 0:L.widthInColumns)??1,w.debug("abc88 columnsPos",L,b)}}w.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${(N=e==null?void 0:e.size)==null?void 0:N.x} y: ${(v=e==null?void 0:e.size)==null?void 0:v.y} width: ${(d=e==null?void 0:e.size)==null?void 0:d.width}`)}function kt(e,{minX:t,minY:a,maxX:n,maxY:r}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:s,y:i,width:o,height:c}=e.size;s-o/2<t&&(t=s-o/2),i-c/2<a&&(a=i-c/2),s+o/2>n&&(n=s+o/2),i+c/2>r&&(r=i+c/2)}if(e.children)for(const s of e.children)({minX:t,minY:a,maxX:n,maxY:r}=kt(s,{minX:t,minY:a,maxX:n,maxY:r}));return{minX:t,minY:a,maxX:n,maxY:r}}function Ut(e){const t=e.getBlock("root");if(!t)return;ot(t,e,0,0),wt(t),w.debug("getBlocks",JSON.stringify(t,null,2));const{minX:a,minY:n,maxX:r,maxY:s}=kt(t);return{x:a,y:n,width:r-a,height:s-n}}function ut(e,t){t&&e.attr("style",t)}function Kt(e){const t=R(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),a=t.append("xhtml:div"),n=e.label,r=e.isNode?"nodeLabel":"edgeLabel",s=a.append("span");return s.html(n),ut(s,e.labelStyle),s.attr("class",r),ut(a,e.labelStyle),a.style("display","inline-block"),a.style("white-space","nowrap"),a.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}h(ot,"setBlockSizes"),h(wt,"layoutBlocks"),h(kt,"findBounds"),h(Ut,"layout"),h(ut,"applyStyle"),h(Kt,"addHtmlLabel");var F=h((e,t,a,n)=>{let r=e||"";if(typeof r=="object"&&(r=r[0]),q(z().flowchart.htmlLabels))return r=r.replace(/\\n|\n/g,"<br />"),w.debug("vertexText"+r),Kt({isNode:n,label:ye(gt(r)),labelStyle:t.replace("fill:","color:")});{const s=document.createElementNS("http://www.w3.org/2000/svg","text");s.setAttribute("style",t.replace("color:","fill:"));let i=[];i=typeof r=="string"?r.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(r)?r:[];for(const o of i){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),a?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=o.trim(),s.appendChild(c)}return s}},"createLabel"),Xe=h((e,t,a,n,r)=>{t.arrowTypeStart&&Nt(e,"start",t.arrowTypeStart,a,n,r),t.arrowTypeEnd&&Nt(e,"end",t.arrowTypeEnd,a,n,r)},"addEdgeMarkers"),Fe={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Nt=h((e,t,a,n,r,s)=>{const i=Fe[a];if(!i)return void w.warn(`Unknown arrow type: ${a}`);const o=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${n}#${r}_${s}-${i}${o})`)},"addEdgeMarker"),xt={},A={},He=h((e,t)=>{const a=z(),n=q(a.flowchart.htmlLabels),r=t.labelType==="markdown"?Pt(e,t.label,{style:t.labelStyle,useHtmlLabels:n,addSvgBackground:!0},a):F(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),i=s.insert("g").attr("class","label");i.node().appendChild(r);let o,c=r.getBBox();if(n){const y=r.children[0],p=R(r);c=y.getBoundingClientRect(),p.attr("width",c.width),p.attr("height",c.height)}if(i.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"),xt[t.id]=s,t.width=c.width,t.height=c.height,t.startLabelLeft){const y=F(t.startLabelLeft,t.labelStyle),p=e.insert("g").attr("class","edgeTerminals"),x=p.insert("g").attr("class","inner");o=x.node().appendChild(y);const f=y.getBBox();x.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"),A[t.id]||(A[t.id]={}),A[t.id].startLeft=p,Q(o,t.startLabelLeft)}if(t.startLabelRight){const y=F(t.startLabelRight,t.labelStyle),p=e.insert("g").attr("class","edgeTerminals"),x=p.insert("g").attr("class","inner");o=p.node().appendChild(y),x.node().appendChild(y);const f=y.getBBox();x.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"),A[t.id]||(A[t.id]={}),A[t.id].startRight=p,Q(o,t.startLabelRight)}if(t.endLabelLeft){const y=F(t.endLabelLeft,t.labelStyle),p=e.insert("g").attr("class","edgeTerminals"),x=p.insert("g").attr("class","inner");o=x.node().appendChild(y);const f=y.getBBox();x.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"),p.node().appendChild(y),A[t.id]||(A[t.id]={}),A[t.id].endLeft=p,Q(o,t.endLabelLeft)}if(t.endLabelRight){const y=F(t.endLabelRight,t.labelStyle),p=e.insert("g").attr("class","edgeTerminals"),x=p.insert("g").attr("class","inner");o=x.node().appendChild(y);const f=y.getBBox();x.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"),p.node().appendChild(y),A[t.id]||(A[t.id]={}),A[t.id].endRight=p,Q(o,t.endLabelRight)}return r},"insertEdgeLabel");function Q(e,t){z().flowchart.htmlLabels&&e&&(e.style.width=9*t.length+"px",e.style.height="12px")}h(Q,"setTerminalWidth");var Ue=h((e,t)=>{w.debug("Moving label abc88 ",e.id,e.label,xt[e.id],t);let a=t.updatedPath?t.updatedPath:t.originalPath;const n=z(),{subGraphTitleTotalMargin:r}=ge(n);if(e.label){const s=xt[e.id];let i=e.x,o=e.y;if(a){const c=V.calcLabelPosition(a);w.debug("Moving label "+e.label+" from (",i,",",o,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(i=c.x,o=c.y)}s.attr("transform",`translate(${i}, ${o+r/2})`)}if(e.startLabelLeft){const s=A[e.id].startLeft;let i=e.x,o=e.y;if(a){const c=V.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",a);i=c.x,o=c.y}s.attr("transform",`translate(${i}, ${o})`)}if(e.startLabelRight){const s=A[e.id].startRight;let i=e.x,o=e.y;if(a){const c=V.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",a);i=c.x,o=c.y}s.attr("transform",`translate(${i}, ${o})`)}if(e.endLabelLeft){const s=A[e.id].endLeft;let i=e.x,o=e.y;if(a){const c=V.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",a);i=c.x,o=c.y}s.attr("transform",`translate(${i}, ${o})`)}if(e.endLabelRight){const s=A[e.id].endRight;let i=e.x,o=e.y;if(a){const c=V.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",a);i=c.x,o=c.y}s.attr("transform",`translate(${i}, ${o})`)}},"positionEdgeLabel"),Ke=h((e,t)=>{const a=e.x,n=e.y,r=Math.abs(t.x-a),s=Math.abs(t.y-n),i=e.width/2,o=e.height/2;return r>=i||s>=o},"outsideNode"),je=h((e,t,a)=>{w.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(a)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const n=e.x,r=e.y,s=Math.abs(n-a.x),i=e.width/2;let o=a.x<t.x?i-s:i+s;const c=e.height/2,y=Math.abs(t.y-a.y),p=Math.abs(t.x-a.x);if(Math.abs(r-t.y)*i>Math.abs(n-t.x)*c){let x=a.y<t.y?t.y-c-r:r-c-t.y;o=p*x/y;const f={x:a.x<t.x?a.x+o:a.x-p+o,y:a.y<t.y?a.y+y-x:a.y-y+x};return o===0&&(f.x=t.x,f.y=t.y),p===0&&(f.x=t.x),y===0&&(f.y=t.y),w.debug(`abc89 topp/bott calc, Q ${y}, q ${x}, R ${p}, r ${o}`,f),f}{o=a.x<t.x?t.x-i-n:n-i-t.x;let x=y*o/p,f=a.x<t.x?a.x+p-o:a.x-p+o,_=a.y<t.y?a.y+x:a.y-x;return w.debug(`sides calc abc89, Q ${y}, q ${x}, R ${p}, r ${o}`,{_x:f,_y:_}),o===0&&(f=t.x,_=t.y),p===0&&(f=t.x),y===0&&(_=t.y),{x:f,y:_}}},"intersection"),It=h((e,t)=>{w.debug("abc88 cutPathAtIntersect",e,t);let a=[],n=e[0],r=!1;return e.forEach(s=>{if(Ke(t,s)||r)n=s,r||a.push(s);else{const i=je(t,n,s);let o=!1;a.forEach(c=>{o=o||c.x===i.x&&c.y===i.y}),a.some(c=>c.x===i.x&&c.y===i.y)||a.push(i),r=!0}}),a},"cutPathAtIntersect"),qe=h(function(e,t,a,n,r,s,i){let o=a.points;w.debug("abc88 InsertEdge: edge=",a,"e=",t);let c=!1;const y=s.node(t.v);var p=s.node(t.w);p!=null&&p.intersect&&(y!=null&&y.intersect)&&(o=o.slice(1,a.points.length-1),o.unshift(y.intersect(o[0])),o.push(p.intersect(o[o.length-1]))),a.toCluster&&(w.debug("to cluster abc88",n[a.toCluster]),o=It(a.points,n[a.toCluster].node),c=!0),a.fromCluster&&(w.debug("from cluster abc88",n[a.fromCluster]),o=It(o.reverse(),n[a.fromCluster].node).reverse(),c=!0);const x=o.filter(u=>!Number.isNaN(u.y));let f=he;!a.curve||r!=="graph"&&r!=="flowchart"||(f=a.curve);const{x:_,y:S}=ce(a),E=de().x(_).y(S).curve(f);let D;switch(a.thickness){case"normal":D="edge-thickness-normal";break;case"thick":case"invisible":D="edge-thickness-thick";break;default:D=""}switch(a.pattern){case"solid":D+=" edge-pattern-solid";break;case"dotted":D+=" edge-pattern-dotted";break;case"dashed":D+=" edge-pattern-dashed"}const N=e.append("path").attr("d",E(x)).attr("id",a.id).attr("class"," "+D+(a.classes?" "+a.classes:"")).attr("style",a.style);let v="";(z().flowchart.arrowMarkerAbsolute||z().state.arrowMarkerAbsolute)&&(v=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,v=v.replace(/\(/g,"\\("),v=v.replace(/\)/g,"\\)")),Xe(N,a,v,i,r);let d={};return c&&(d.updatedPath=o),d.originalPath=a.points,d},"insertEdge"),Ze=h(e=>{const t=new Set;for(const a of e)switch(a){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(a)}return t},"expandAndDeduplicateDirections"),Ge=h((e,t,a)=>{const n=Ze(e),r=t.height+2*a.padding,s=r/2,i=t.width+2*s+a.padding,o=a.padding/2;return n.has("right")&&n.has("left")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:s,y:0},{x:i/2,y:2*o},{x:i-s,y:0},{x:i,y:0},{x:i,y:-r/3},{x:i+2*o,y:-r/2},{x:i,y:-2*r/3},{x:i,y:-r},{x:i-s,y:-r},{x:i/2,y:-r-2*o},{x:s,y:-r},{x:0,y:-r},{x:0,y:-2*r/3},{x:-2*o,y:-r/2},{x:0,y:-r/3}]:n.has("right")&&n.has("left")&&n.has("up")?[{x:s,y:0},{x:i-s,y:0},{x:i,y:-r/2},{x:i-s,y:-r},{x:s,y:-r},{x:0,y:-r/2}]:n.has("right")&&n.has("left")&&n.has("down")?[{x:0,y:0},{x:s,y:-r},{x:i-s,y:-r},{x:i,y:0}]:n.has("right")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:i,y:-s},{x:i,y:-r+s},{x:0,y:-r}]:n.has("left")&&n.has("up")&&n.has("down")?[{x:i,y:0},{x:0,y:-s},{x:0,y:-r+s},{x:i,y:-r}]:n.has("right")&&n.has("left")?[{x:s,y:0},{x:s,y:-o},{x:i-s,y:-o},{x:i-s,y:0},{x:i,y:-r/2},{x:i-s,y:-r},{x:i-s,y:-r+o},{x:s,y:-r+o},{x:s,y:-r},{x:0,y:-r/2}]:n.has("up")&&n.has("down")?[{x:i/2,y:0},{x:0,y:-o},{x:s,y:-o},{x:s,y:-r+o},{x:0,y:-r+o},{x:i/2,y:-r},{x:i,y:-r+o},{x:i-s,y:-r+o},{x:i-s,y:-o},{x:i,y:-o}]:n.has("right")&&n.has("up")?[{x:0,y:0},{x:i,y:-s},{x:0,y:-r}]:n.has("right")&&n.has("down")?[{x:0,y:0},{x:i,y:0},{x:0,y:-r}]:n.has("left")&&n.has("up")?[{x:i,y:0},{x:0,y:-s},{x:i,y:-r}]:n.has("left")&&n.has("down")?[{x:i,y:0},{x:0,y:0},{x:i,y:-r}]:n.has("right")?[{x:s,y:-o},{x:s,y:-o},{x:i-s,y:-o},{x:i-s,y:0},{x:i,y:-r/2},{x:i-s,y:-r},{x:i-s,y:-r+o},{x:s,y:-r+o},{x:s,y:-r+o}]:n.has("left")?[{x:s,y:0},{x:s,y:-o},{x:i-s,y:-o},{x:i-s,y:-r+o},{x:s,y:-r+o},{x:s,y:-r},{x:0,y:-r/2}]:n.has("up")?[{x:s,y:-o},{x:s,y:-r+o},{x:0,y:-r+o},{x:i/2,y:-r},{x:i,y:-r+o},{x:i-s,y:-r+o},{x:i-s,y:-o}]:n.has("down")?[{x:i/2,y:0},{x:0,y:-o},{x:s,y:-o},{x:s,y:-r+o},{x:i-s,y:-r+o},{x:i-s,y:-o},{x:i,y:-o}]:[{x:0,y:0}]},"getArrowPoints");function jt(e,t){return e.intersect(t)}h(jt,"intersectNode");var Je=jt;function qt(e,t,a,n){var r=e.x,s=e.y,i=r-n.x,o=s-n.y,c=Math.sqrt(t*t*o*o+a*a*i*i),y=Math.abs(t*a*i/c);n.x<r&&(y=-y);var p=Math.abs(t*a*o/c);return n.y<s&&(p=-p),{x:r+y,y:s+p}}h(qt,"intersectEllipse");var Zt=qt;function Gt(e,t,a){return Zt(e,t,t,a)}h(Gt,"intersectCircle");var Ve=Gt;function Jt(e,t,a,n){var r,s,i,o,c,y,p,x,f,_,S,E,D;if(r=t.y-e.y,i=e.x-t.x,c=t.x*e.y-e.x*t.y,f=r*a.x+i*a.y+c,_=r*n.x+i*n.y+c,!(f!==0&&_!==0&&bt(f,_)||(s=n.y-a.y,o=a.x-n.x,y=n.x*a.y-a.x*n.y,p=s*e.x+o*e.y+y,x=s*t.x+o*t.y+y,p!==0&&x!==0&&bt(p,x)||(S=r*o-s*i)==0)))return E=Math.abs(S/2),{x:(D=i*y-o*c)<0?(D-E)/S:(D+E)/S,y:(D=s*c-r*y)<0?(D-E)/S:(D+E)/S}}function bt(e,t){return e*t>0}h(Jt,"intersectLine"),h(bt,"sameSign");var Qe=Jt,tr=Vt;function Vt(e,t,a){var n=e.x,r=e.y,s=[],i=Number.POSITIVE_INFINITY,o=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(S){i=Math.min(i,S.x),o=Math.min(o,S.y)}):(i=Math.min(i,t.x),o=Math.min(o,t.y));for(var c=n-e.width/2-i,y=r-e.height/2-o,p=0;p<t.length;p++){var x=t[p],f=t[p<t.length-1?p+1:0],_=Qe(e,a,{x:c+x.x,y:y+x.y},{x:c+f.x,y:y+f.y});_&&s.push(_)}return s.length?(s.length>1&&s.sort(function(S,E){var D=S.x-a.x,N=S.y-a.y,v=Math.sqrt(D*D+N*N),d=E.x-a.x,u=E.y-a.y,k=Math.sqrt(d*d+u*u);return v<k?-1:v===k?0:1}),s[0]):e}h(Vt,"intersectPolygon");var C={node:Je,circle:Ve,ellipse:Zt,polygon:tr,rect:h((e,t)=>{var a,n,r=e.x,s=e.y,i=t.x-r,o=t.y-s,c=e.width/2,y=e.height/2;return Math.abs(o)*c>Math.abs(i)*y?(o<0&&(y=-y),a=o===0?0:y*i/o,n=y):(i<0&&(c=-c),a=c,n=i===0?0:c*o/i),{x:r+a,y:s+n}},"intersectRect")},O=h(async(e,t,a,n)=>{const r=z();let s;const i=t.useHtmlLabels||q(r.flowchart.htmlLabels);s=a||"node default";const o=e.insert("g").attr("class",s).attr("id",t.domId||t.id),c=o.insert("g").attr("class","label").attr("style",t.labelStyle);let y;y=t.labelText===void 0?"":typeof t.labelText=="string"?t.labelText:t.labelText[0];const p=c.node();let x;x=t.labelType==="markdown"?Pt(c,Dt(gt(y),r),{useHtmlLabels:i,width:t.width||r.flowchart.wrappingWidth,classes:"markdown-node-label"},r):p.appendChild(F(Dt(gt(y),r),t.labelStyle,!1,n));let f=x.getBBox();const _=t.padding/2;if(q(r.flowchart.htmlLabels)){const S=x.children[0],E=R(x),D=S.getElementsByTagName("img");if(D){const N=y.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...D].map(v=>new Promise(d=>{function u(){if(v.style.display="flex",v.style.flexDirection="column",N){const k=r.fontSize?r.fontSize:window.getComputedStyle(document.body).fontSize,b=5,m=parseInt(k,10)*b+"px";v.style.minWidth=m,v.style.maxWidth=m}else v.style.width="100%";d(v)}h(u,"setupImage"),setTimeout(()=>{v.complete&&u()}),v.addEventListener("error",u),v.addEventListener("load",u)})))}f=S.getBoundingClientRect(),E.attr("width",f.width),E.attr("height",f.height)}return i?c.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"):c.attr("transform","translate(0, "+-f.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-f.width/2+", "+-f.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:o,bbox:f,halfPadding:_,label:c}},"labelHelper"),I=h((e,t)=>{const a=t.node().getBBox();e.width=a.width,e.height=a.height},"updateNodeBounds");function j(e,t,a,n){return e.insert("polygon",":first-child").attr("points",n.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+a/2+")")}h(j,"insertPolygonShape");var er=h(async(e,t)=>{t.useHtmlLabels||z().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:a,bbox:n,halfPadding:r}=await O(e,t,"node "+t.classes,!0);w.info("Classes = ",t.classes);const s=a.insert("rect",":first-child");return s.attr("rx",t.rx).attr("ry",t.ry).attr("x",-n.width/2-r).attr("y",-n.height/2-r).attr("width",n.width+t.padding).attr("height",n.height+t.padding),I(t,s),t.intersect=function(i){return C.rect(t,i)},a},"note"),Bt=h(e=>e?" "+e:"","formatClass"),Y=h((e,t)=>`${t||"node default"}${Bt(e.classes)} ${Bt(e.class)}`,"getClassesFromNode"),zt=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding+(n.height+t.padding),s=[{x:r/2,y:0},{x:r,y:-r/2},{x:r/2,y:-r},{x:0,y:-r/2}];w.info("Question main (Circle)");const i=j(a,r,r,s);return i.attr("style",t.style),I(t,i),t.intersect=function(o){return w.warn("Intersect called"),C.polygon(t,s,o)},a},"question"),rr=h((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=[{x:0,y:14},{x:14,y:0},{x:0,y:-14},{x:-14,y:0}];return a.insert("polygon",":first-child").attr("points",n.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(r){return C.circle(t,14,r)},a},"choice"),ar=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.height+t.padding,s=r/4,i=n.width+2*s+t.padding,o=[{x:s,y:0},{x:i-s,y:0},{x:i,y:-r/2},{x:i-s,y:-r},{x:s,y:-r},{x:0,y:-r/2}],c=j(a,i,r,o);return c.attr("style",t.style),I(t,c),t.intersect=function(y){return C.polygon(t,o,y)},a},"hexagon"),sr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,void 0,!0),r=n.height+2*t.padding,s=r/2,i=n.width+2*s+t.padding,o=Ge(t.directions,n,t),c=j(a,i,r,o);return c.attr("style",t.style),I(t,c),t.intersect=function(y){return C.polygon(t,o,y)},a},"block_arrow"),nr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:-s/2,y:0},{x:r,y:0},{x:r,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return j(a,r,s,i).attr("style",t.style),t.width=r+s,t.height=s,t.intersect=function(o){return C.polygon(t,i,o)},a},"rect_left_inv_arrow"),ir=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:-2*s/6,y:0},{x:r-s/6,y:0},{x:r+2*s/6,y:-s},{x:s/6,y:-s}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"lean_right"),or=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:2*s/6,y:0},{x:r+s/6,y:0},{x:r-2*s/6,y:-s},{x:-s/6,y:-s}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"lean_left"),lr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:-2*s/6,y:0},{x:r+2*s/6,y:0},{x:r-s/6,y:-s},{x:s/6,y:-s}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"trapezoid"),cr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:s/6,y:0},{x:r-s/6,y:0},{x:r+2*s/6,y:-s},{x:-2*s/6,y:-s}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"inv_trapezoid"),dr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:0,y:0},{x:r+s/2,y:0},{x:r,y:-s/2},{x:r+s/2,y:-s},{x:0,y:-s}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"rect_right_inv_arrow"),hr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=r/2,i=s/(2.5+r/50),o=n.height+i+t.padding,c="M 0,"+i+" a "+s+","+i+" 0,0,0 "+r+" 0 a "+s+","+i+" 0,0,0 "+-r+" 0 l 0,"+o+" a "+s+","+i+" 0,0,0 "+r+" 0 l 0,"+-o,y=a.attr("label-offset-y",i).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-r/2+","+-(o/2+i)+")");return I(t,y),t.intersect=function(p){const x=C.rect(t,p),f=x.x-t.x;if(s!=0&&(Math.abs(f)<t.width/2||Math.abs(f)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-i)){let _=i*i*(1-f*f/(s*s));_!=0&&(_=Math.sqrt(_)),_=i-_,p.y-t.y>0&&(_=-_),x.y+=_}return x},a},"cylinder"),gr=h(async(e,t)=>{const{shapeSvg:a,bbox:n,halfPadding:r}=await O(e,t,"node "+t.classes+" "+t.class,!0),s=a.insert("rect",":first-child"),i=t.positioned?t.width:n.width+t.padding,o=t.positioned?t.height:n.height+t.padding,c=t.positioned?-i/2:-n.width/2-r,y=t.positioned?-o/2:-n.height/2-r;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",y).attr("width",i).attr("height",o),t.props){const p=new Set(Object.keys(t.props));t.props.borders&&(lt(s,t.props.borders,i,o),p.delete("borders")),p.forEach(x=>{w.warn(`Unknown node property ${x}`)})}return I(t,s),t.intersect=function(p){return C.rect(t,p)},a},"rect"),pr=h(async(e,t)=>{const{shapeSvg:a,bbox:n,halfPadding:r}=await O(e,t,"node "+t.classes,!0),s=a.insert("rect",":first-child"),i=t.positioned?t.width:n.width+t.padding,o=t.positioned?t.height:n.height+t.padding,c=t.positioned?-i/2:-n.width/2-r,y=t.positioned?-o/2:-n.height/2-r;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",y).attr("width",i).attr("height",o),t.props){const p=new Set(Object.keys(t.props));t.props.borders&&(lt(s,t.props.borders,i,o),p.delete("borders")),p.forEach(x=>{w.warn(`Unknown node property ${x}`)})}return I(t,s),t.intersect=function(p){return C.rect(t,p)},a},"composite"),yr=h(async(e,t)=>{const{shapeSvg:a}=await O(e,t,"label",!0);w.trace("Classes = ",t.class);const n=a.insert("rect",":first-child");if(n.attr("width",0).attr("height",0),a.attr("class","label edgeLabel"),t.props){const r=new Set(Object.keys(t.props));t.props.borders&&(lt(n,t.props.borders,0,0),r.delete("borders")),r.forEach(s=>{w.warn(`Unknown node property ${s}`)})}return I(t,n),t.intersect=function(r){return C.rect(t,r)},a},"labelRect");function lt(e,t,a,n){const r=[],s=h(o=>{r.push(o,0)},"addBorder"),i=h(o=>{r.push(0,o)},"skipBorder");t.includes("t")?(w.debug("add top border"),s(a)):i(a),t.includes("r")?(w.debug("add right border"),s(n)):i(n),t.includes("b")?(w.debug("add bottom border"),s(a)):i(a),t.includes("l")?(w.debug("add left border"),s(n)):i(n),e.attr("stroke-dasharray",r.join(" "))}h(lt,"applyNodePropertyBorders");var ur=h((e,t)=>{let a;a=t.classes?"node "+t.classes:"node default";const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),r=n.insert("rect",":first-child"),s=n.insert("line"),i=n.insert("g").attr("class","label"),o=t.labelText.flat?t.labelText.flat():t.labelText;let c="";c=typeof o=="object"?o[0]:o,w.info("Label text abc79",c,o,typeof o=="object");const y=i.node().appendChild(F(c,t.labelStyle,!0,!0));let p={width:0,height:0};if(q(z().flowchart.htmlLabels)){const E=y.children[0],D=R(y);p=E.getBoundingClientRect(),D.attr("width",p.width),D.attr("height",p.height)}w.info("Text 2",o);const x=o.slice(1,o.length);let f=y.getBBox();const _=i.node().appendChild(F(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(q(z().flowchart.htmlLabels)){const E=_.children[0],D=R(_);p=E.getBoundingClientRect(),D.attr("width",p.width),D.attr("height",p.height)}const S=t.padding/2;return R(_).attr("transform","translate( "+(p.width>f.width?0:(f.width-p.width)/2)+", "+(f.height+S+5)+")"),R(y).attr("transform","translate( "+(p.width<f.width?0:-(f.width-p.width)/2)+", 0)"),p=i.node().getBBox(),i.attr("transform","translate("+-p.width/2+", "+(-p.height/2-S+3)+")"),r.attr("class","outer title-state").attr("x",-p.width/2-S).attr("y",-p.height/2-S).attr("width",p.width+t.padding).attr("height",p.height+t.padding),s.attr("class","divider").attr("x1",-p.width/2-S).attr("x2",p.width/2+S).attr("y1",-p.height/2-S+f.height+S).attr("y2",-p.height/2-S+f.height+S),I(t,r),t.intersect=function(E){return C.rect(t,E)},n},"rectWithTitle"),xr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.height+t.padding,s=n.width+r/4+t.padding,i=a.insert("rect",":first-child").attr("style",t.style).attr("rx",r/2).attr("ry",r/2).attr("x",-s/2).attr("y",-r/2).attr("width",s).attr("height",r);return I(t,i),t.intersect=function(o){return C.rect(t,o)},a},"stadium"),br=h(async(e,t)=>{const{shapeSvg:a,bbox:n,halfPadding:r}=await O(e,t,Y(t,void 0),!0),s=a.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+r).attr("width",n.width+t.padding).attr("height",n.height+t.padding),w.info("Circle main"),I(t,s),t.intersect=function(i){return w.info("Circle intersect",t,n.width/2+r,i),C.circle(t,n.width/2+r,i)},a},"circle"),fr=h(async(e,t)=>{const{shapeSvg:a,bbox:n,halfPadding:r}=await O(e,t,Y(t,void 0),!0),s=a.insert("g",":first-child"),i=s.insert("circle"),o=s.insert("circle");return s.attr("class",t.class),i.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+r+5).attr("width",n.width+t.padding+10).attr("height",n.height+t.padding+10),o.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+r).attr("width",n.width+t.padding).attr("height",n.height+t.padding),w.info("DoubleCircle main"),I(t,i),t.intersect=function(c){return w.info("DoubleCircle intersect",t,n.width/2+r+5,c),C.circle(t,n.width/2+r+5,c)},a},"doublecircle"),mr=h(async(e,t)=>{const{shapeSvg:a,bbox:n}=await O(e,t,Y(t,void 0),!0),r=n.width+t.padding,s=n.height+t.padding,i=[{x:0,y:0},{x:r,y:0},{x:r,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:r+8,y:0},{x:r+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],o=j(a,r,s,i);return o.attr("style",t.style),I(t,o),t.intersect=function(c){return C.polygon(t,i,c)},a},"subroutine"),wr=h((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=a.insert("circle",":first-child");return n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),I(t,n),t.intersect=function(r){return C.circle(t,7,r)},a},"start"),Rt=h((e,t,a)=>{const n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let r=70,s=10;a==="LR"&&(r=10,s=70);const i=n.append("rect").attr("x",-1*r/2).attr("y",-1*s/2).attr("width",r).attr("height",s).attr("class","fork-join");return I(t,i),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(o){return C.rect(t,o)},n},"forkJoin"),Ot={rhombus:zt,composite:pr,question:zt,rect:gr,labelRect:yr,rectWithTitle:ur,choice:rr,circle:br,doublecircle:fr,stadium:xr,hexagon:ar,block_arrow:sr,rect_left_inv_arrow:nr,lean_right:ir,lean_left:or,trapezoid:lr,inv_trapezoid:cr,rect_right_inv_arrow:dr,cylinder:hr,start:wr,end:h((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=a.insert("circle",":first-child"),r=a.insert("circle",":first-child");return r.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),n.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),I(t,r),t.intersect=function(s){return C.circle(t,7,s)},a},"end"),note:er,subroutine:mr,fork:Rt,join:Rt,class_box:h((e,t)=>{var b;const a=t.padding/2;let n;n=t.classes?"node "+t.classes:"node default";const r=e.insert("g").attr("class",n).attr("id",t.domId||t.id),s=r.insert("rect",":first-child"),i=r.insert("line"),o=r.insert("line");let c=0,y=4;const p=r.insert("g").attr("class","label");let x=0;const f=(b=t.classData.annotations)==null?void 0:b[0],_=t.classData.annotations[0]?"«"+t.classData.annotations[0]+"»":"",S=p.node().appendChild(F(_,t.labelStyle,!0,!0));let E=S.getBBox();if(q(z().flowchart.htmlLabels)){const m=S.children[0],l=R(S);E=m.getBoundingClientRect(),l.attr("width",E.width),l.attr("height",E.height)}t.classData.annotations[0]&&(y+=E.height+4,c+=E.width);let D=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(z().flowchart.htmlLabels?D+="&lt;"+t.classData.type+"&gt;":D+="<"+t.classData.type+">");const N=p.node().appendChild(F(D,t.labelStyle,!0,!0));R(N).attr("class","classTitle");let v=N.getBBox();if(q(z().flowchart.htmlLabels)){const m=N.children[0],l=R(N);v=m.getBoundingClientRect(),l.attr("width",v.width),l.attr("height",v.height)}y+=v.height+4,v.width>c&&(c=v.width);const d=[];t.classData.members.forEach(m=>{const l=m.getDisplayDetails();let L=l.displayText;z().flowchart.htmlLabels&&(L=L.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const g=p.node().appendChild(F(L,l.cssStyle?l.cssStyle:t.labelStyle,!0,!0));let $=g.getBBox();if(q(z().flowchart.htmlLabels)){const H=g.children[0],U=R(g);$=H.getBoundingClientRect(),U.attr("width",$.width),U.attr("height",$.height)}$.width>c&&(c=$.width),y+=$.height+4,d.push(g)}),y+=8;const u=[];if(t.classData.methods.forEach(m=>{const l=m.getDisplayDetails();let L=l.displayText;z().flowchart.htmlLabels&&(L=L.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const g=p.node().appendChild(F(L,l.cssStyle?l.cssStyle:t.labelStyle,!0,!0));let $=g.getBBox();if(q(z().flowchart.htmlLabels)){const H=g.children[0],U=R(g);$=H.getBoundingClientRect(),U.attr("width",$.width),U.attr("height",$.height)}$.width>c&&(c=$.width),y+=$.height+4,u.push(g)}),y+=8,f){let m=(c-E.width)/2;R(S).attr("transform","translate( "+(-1*c/2+m)+", "+-1*y/2+")"),x=E.height+4}let k=(c-v.width)/2;return R(N).attr("transform","translate( "+(-1*c/2+k)+", "+(-1*y/2+x)+")"),x+=v.height+4,i.attr("class","divider").attr("x1",-c/2-a).attr("x2",c/2+a).attr("y1",-y/2-a+8+x).attr("y2",-y/2-a+8+x),x+=8,d.forEach(m=>{R(m).attr("transform","translate( "+-c/2+", "+(-1*y/2+x+4)+")");const l=m==null?void 0:m.getBBox();x+=((l==null?void 0:l.height)??0)+4}),x+=8,o.attr("class","divider").attr("x1",-c/2-a).attr("x2",c/2+a).attr("y1",-y/2-a+8+x).attr("y2",-y/2-a+8+x),x+=8,u.forEach(m=>{R(m).attr("transform","translate( "+-c/2+", "+(-1*y/2+x)+")");const l=m==null?void 0:m.getBBox();x+=((l==null?void 0:l.height)??0)+4}),s.attr("style",t.style).attr("class","outer title-state").attr("x",-c/2-a).attr("y",-y/2-a).attr("width",c+t.padding).attr("height",y+t.padding),I(t,s),t.intersect=function(m){return C.rect(t,m)},r},"class_box")},nt={},Qt=h(async(e,t,a)=>{let n,r;if(t.link){let s;z().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),n=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),r=await Ot[t.shape](n,t,a)}else r=await Ot[t.shape](e,t,a),n=r;return t.tooltip&&r.attr("title",t.tooltip),t.class&&r.attr("class","node default "+t.class),nt[t.id]=n,t.haveCallback&&nt[t.id].attr("class",nt[t.id].attr("class")+" clickable"),n},"insertNode"),kr=h(e=>{const t=nt[e.id];w.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const a=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+a-e.width/2)+", "+(e.y-e.height/2-8)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),a},"positionNode");function St(e,t,a=!1){var x,f,_;const n=e;let r="default";(((x=n==null?void 0:n.classes)==null?void 0:x.length)||0)>0&&(r=((n==null?void 0:n.classes)??[]).join(" ")),r+=" flowchart-label";let s,i=0,o="";switch(n.type){case"round":i=5,o="rect";break;case"composite":i=0,o="composite",s=0;break;case"square":case"group":default:o="rect";break;case"diamond":o="question";break;case"hexagon":o="hexagon";break;case"block_arrow":o="block_arrow";break;case"odd":case"rect_left_inv_arrow":o="rect_left_inv_arrow";break;case"lean_right":o="lean_right";break;case"lean_left":o="lean_left";break;case"trapezoid":o="trapezoid";break;case"inv_trapezoid":o="inv_trapezoid";break;case"circle":o="circle";break;case"ellipse":o="ellipse";break;case"stadium":o="stadium";break;case"subroutine":o="subroutine";break;case"cylinder":o="cylinder";break;case"doublecircle":o="doublecircle"}const c=ue((n==null?void 0:n.styles)??[]),y=n.label,p=n.size??{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:o,labelText:y,rx:i,ry:i,class:r,style:c.style,id:n.id,directions:n.directions,width:p.width,height:p.height,x:p.x,y:p.y,positioned:a,intersect:void 0,type:n.type,padding:s??((_=(f=et())==null?void 0:f.block)==null?void 0:_.padding)??0}}async function te(e,t,a){const n=St(t,0,!1);if(n.type==="group")return;const r=et(),s=await Qt(e,n,{config:r}),i=s.node().getBBox(),o=a.getBlock(n.id);o.size={width:i.width,height:i.height,x:0,y:0,node:s},a.setBlock(o),s.remove()}async function ee(e,t,a){const n=St(t,0,!0);if(a.getBlock(n.id).type!=="space"){const r=et();await Qt(e,n,{config:r}),t.intersect=n==null?void 0:n.intersect,kr(n)}}async function ct(e,t,a,n){for(const r of t)await n(e,r,a),r.children&&await ct(e,r.children,a,n)}async function re(e,t,a){await ct(e,t,a,te)}async function ae(e,t,a){await ct(e,t,a,ee)}async function se(e,t,a,n,r){const s=new be({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const i of a)i.size&&s.setNode(i.id,{width:i.size.width,height:i.size.height,intersect:i.intersect});for(const i of t)if(i.start&&i.end){const o=n.getBlock(i.start),c=n.getBlock(i.end);if(o!=null&&o.size&&(c!=null&&c.size)){const y=o.size,p=c.size,x=[{x:y.x,y:y.y},{x:y.x+(p.x-y.x)/2,y:y.y+(p.y-y.y)/2},{x:p.x,y:p.y}];qe(e,{v:i.start,w:i.end,name:i.id},{...i,arrowTypeEnd:i.arrowTypeEnd,arrowTypeStart:i.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",s,r),i.label&&(await He(e,{...i,label:i.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:i.arrowTypeEnd,arrowTypeStart:i.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),Ue({...i,x:x[1].x,y:x[1].y},{originalPath:x}))}}}h(St,"getNodeFromBlock"),h(te,"calculateBlockSize"),h(ee,"insertBlockPositioned"),h(ct,"performOperations"),h(re,"calculateBlockSizes"),h(ae,"insertBlocks"),h(se,"insertEdges");var Sr=h(function(e,t){return t.db.getClasses()},"getClasses"),ua={parser:me,db:Oe,renderer:{draw:h(async function(e,t,a,n){const{securityLevel:r,block:s}=et(),i=n.db;let o;r==="sandbox"&&(o=R("#i"+t));const c=R(r==="sandbox"?o.nodes()[0].contentDocument.body:"body"),y=r==="sandbox"?c.select(`[id="${t}"]`):R(`[id="${t}"]`);Ye(y,["point","circle","cross"],n.type,t);const p=i.getBlocks(),x=i.getBlocksFlat(),f=i.getEdges(),_=y.insert("g").attr("class","block");await re(_,p,i);const S=Ut(i);if(await ae(_,p,i),await se(_,f,x,i,t),S){const E=S,D=Math.max(1,Math.round(E.width/E.height*.125)),N=E.height+D+10,v=E.width+10,{useMaxWidth:d}=s;ie(y,N,v,!!d),w.debug("Here Bounds",S,E),y.attr("viewBox",`${E.x-5} ${E.y-5} ${E.width+10} ${E.height+10}`)}},"draw"),getClasses:Sr},styles:Ae};export{ua as diagram};

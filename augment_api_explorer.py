#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment API 探索器
基于发现的 /api/augment 端点进行深入探索
"""

import requests
import json
import time

class AugmentAPIExplorer:
    def __init__(self):
        self.base_url = "http://localhost:63342"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'AugmentAPIExplorer/1.0'
        })
    
    def explore_augment_endpoints(self):
        """探索 /api/augment 下的子端点"""
        print("🔍 探索 /api/augment 子端点...")
        
        # 常见的子端点
        sub_endpoints = [
            "",  # 基础端点
            "/chat",
            "/message",
            "/send",
            "/query",
            "/ask",
            "/completion",
            "/stream",
            "/status",
            "/health",
            "/info",
            "/config",
            "/session",
            "/auth",
            "/user",
            "/conversation",
            "/history"
        ]
        
        available_endpoints = []
        
        for sub in sub_endpoints:
            endpoint = f"/api/augment{sub}"
            try:
                # 尝试GET
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=5)
                status = response.status_code
                
                if status != 404:
                    print(f"✅ GET {endpoint}: {status}")
                    available_endpoints.append((endpoint, "GET", status))
                    
                    if status == 200 and response.text:
                        print(f"   响应: {response.text[:150]}...")
                
                # 尝试POST
                test_payload = {"text": "test", "message": "hello"}
                response = self.session.post(f"{self.base_url}{endpoint}", json=test_payload, timeout=5)
                status = response.status_code
                
                if status != 404:
                    print(f"✅ POST {endpoint}: {status}")
                    available_endpoints.append((endpoint, "POST", status))
                    
                    if status == 200 and response.text:
                        print(f"   响应: {response.text[:150]}...")
                        
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
        
        return available_endpoints
    
    def test_chat_payloads(self, endpoint):
        """测试不同的聊天消息格式"""
        print(f"\n🧪 测试聊天消息格式: {endpoint}")
        
        # 不同的消息格式
        payloads = [
            # 格式1：简单文本
            {"text": "Hello Augment AI"},
            
            # 格式2：标准聊天格式
            {
                "text": "解释这个项目",
                "chatHistory": [],
                "modelId": "default"
            },
            
            # 格式3：完整格式
            {
                "text": "分析代码结构",
                "chatHistory": [],
                "modelId": "default",
                "disableRetrieval": False,
                "silent": False
            },
            
            # 格式4：WebView消息格式
            {
                "type": "chat-user-message",
                "data": {
                    "text": "Hello from WebView format",
                    "chatHistory": []
                }
            },
            
            # 格式5：异步包装格式
            {
                "requestId": str(int(time.time() * 1000)),
                "baseMsg": {
                    "type": "chat-user-message",
                    "data": {
                        "text": "Hello from async format",
                        "chatHistory": []
                    }
                }
            }
        ]
        
        for i, payload in enumerate(payloads, 1):
            print(f"\n📤 测试格式 {i}: {json.dumps(payload, ensure_ascii=False)[:100]}...")
            
            try:
                response = self.session.post(f"{self.base_url}{endpoint}", json=payload, timeout=10)
                print(f"   状态码: {response.status_code}")
                
                if response.text:
                    print(f"   响应: {response.text[:200]}...")
                    
                if response.status_code == 200:
                    print("   ✅ 成功！这个格式可能有效")
                    return payload  # 返回成功的格式
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        return None
    
    def send_real_message(self, endpoint, successful_format):
        """使用成功的格式发送真实消息"""
        print(f"\n🚀 使用成功格式发送真实消息...")
        
        # 基于成功格式创建真实消息
        if "data" in successful_format:
            # WebView格式
            payload = successful_format.copy()
            payload["data"]["text"] = "请解释一下当前项目的主要功能和结构"
        elif "baseMsg" in successful_format:
            # 异步格式
            payload = successful_format.copy()
            payload["baseMsg"]["data"]["text"] = "请分析当前代码库的技术栈"
            payload["requestId"] = str(int(time.time() * 1000))
        else:
            # 简单格式
            payload = successful_format.copy()
            payload["text"] = "你好，Augment AI！请介绍一下你的功能。"
        
        try:
            print(f"📤 发送消息: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            response = self.session.post(f"{self.base_url}{endpoint}", json=payload, timeout=30)
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"📝 完整响应: {response.text}")
            
            if response.status_code == 200:
                print("🎉 消息发送成功！")
                return response.json() if response.text else None
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
        
        return None

def main():
    print("🤖 Augment API 探索器")
    print("基于发现的 /api/augment 端点进行深入探索")
    print("=" * 60)
    
    explorer = AugmentAPIExplorer()
    
    # 1. 探索可用端点
    available_endpoints = explorer.explore_augment_endpoints()
    
    if not available_endpoints:
        print("❌ 未发现可用的Augment API端点")
        return
    
    print(f"\n✅ 发现 {len(available_endpoints)} 个可用端点:")
    for endpoint, method, status in available_endpoints:
        print(f"   {method} {endpoint} -> {status}")
    
    # 2. 测试聊天功能
    print("\n" + "=" * 60)
    print("🧪 测试聊天功能...")
    
    # 优先测试POST端点
    post_endpoints = [ep for ep, method, status in available_endpoints if method == "POST"]
    
    for endpoint, _, _ in post_endpoints:
        print(f"\n🎯 测试端点: {endpoint}")
        successful_format = explorer.test_chat_payloads(endpoint)
        
        if successful_format:
            print(f"\n🎉 找到有效格式！端点: {endpoint}")
            
            # 发送真实消息
            result = explorer.send_real_message(endpoint, successful_format)
            
            if result:
                print("\n🎊 成功与Augment AI通信！")
                break
        else:
            print(f"\n❌ 端点 {endpoint} 未找到有效格式")
    
    print("\n" + "=" * 60)
    print("🏁 探索完成！")

if __name__ == "__main__":
    main()

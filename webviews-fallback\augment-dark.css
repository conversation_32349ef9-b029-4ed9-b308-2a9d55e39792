:root {
  --intellij-actionButton-focusedBorderColor: rgba(94, 172, 208, 1);
  --intellij-actionButton-hoverBackground: rgba(76, 80, 82, 1);
  --intellij-actionButton-hoverBorderColor: rgba(76, 80, 82, 1);
  --intellij-actionButton-pressedBackground: rgba(92, 97, 100, 1);
  --intellij-actionButton-pressedBorderColor: rgba(92, 97, 100, 1);
  --intellij-activeCaption: rgba(67, 78, 96, 1);
  --intellij-activeCaptionBorder: rgba(255, 255, 255, 1);
  --intellij-activeCaptionText: rgba(0, 0, 0, 1);
  --intellij-appInspector-graphNode-background: rgba(81, 86, 88, 1);
  --intellij-bookmark-iconBackground: rgba(170, 133, 66, 1);
  --intellij-bookmark-mnemonic-iconBackground: rgba(91, 83, 65, 1);
  --intellij-bookmark-mnemonic-iconBorderColor: rgba(217, 163, 67, 1);
  --intellij-bookmark-mnemonic-iconForeground: rgba(187, 187, 187, 1);
  --intellij-bookmarkMnemonicAssigned-background: rgba(102, 86, 50, 1);
  --intellij-bookmarkMnemonicAssigned-borderColor: rgba(102, 86, 50, 1);
  --intellij-bookmarkMnemonicAssigned-foreground: rgba(187, 187, 187, 1);
  --intellij-bookmarkMnemonicAvailable-background: rgba(60, 63, 65, 1);
  --intellij-bookmarkMnemonicAvailable-borderColor: rgba(94, 96, 96, 1);
  --intellij-bookmarkMnemonicAvailable-foreground: rgba(187, 187, 187, 1);
  --intellij-bookmarkMnemonicCurrent-background: rgba(52, 95, 133, 1);
  --intellij-bookmarkMnemonicCurrent-borderColor: rgba(52, 95, 133, 1);
  --intellij-bookmarkMnemonicCurrent-foreground: rgba(254, 254, 254, 1);
  --intellij-borders-contrastBorderColor: rgba(50, 50, 50, 1);
  --intellij-button-background: rgba(60, 63, 65, 1);
  --intellij-button-darkShadow: rgba(0, 0, 0, 1);
  --intellij-button-default-endBackground: rgba(54, 88, 128, 1);
  --intellij-button-default-endBorderColor: rgba(76, 112, 140, 1);
  --intellij-button-default-focusColor: rgba(67, 104, 140, 1);
  --intellij-button-default-focusedBorderColor: rgba(83, 118, 153, 1);
  --intellij-button-default-foreground: rgba(187, 187, 187, 1);
  --intellij-button-default-startBackground: rgba(54, 88, 128, 1);
  --intellij-button-default-startBorderColor: rgba(76, 112, 140, 1);
  --intellij-button-disabledBorderColor: rgba(94, 96, 96, 1);
  --intellij-button-disabledText: rgba(119, 119, 119, 1);
  --intellij-button-endBackground: rgba(76, 80, 82, 1);
  --intellij-button-endBorderColor: rgba(94, 96, 96, 1);
  --intellij-button-focusedBorderColor: rgba(70, 109, 148, 1);
  --intellij-button-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-button-font-size: 13px;
  --intellij-button-foreground: rgba(187, 187, 187, 1);
  --intellij-button-highlight: rgba(255, 255, 255, 1);
  --intellij-button-light: rgba(8, 74, 217, 1);
  --intellij-button-select: rgba(255, 102, 102, 1);
  --intellij-button-shadow: rgba(0, 0, 0, 0.27);
  --intellij-button-shadowColor: rgba(54, 54, 54, 0.5);
  --intellij-button-startBackground: rgba(76, 80, 82, 1);
  --intellij-button-startBorderColor: rgba(94, 96, 96, 1);
  --intellij-canvas-tooltip-background: rgba(74, 76, 76, 1);
  --intellij-checkBox-background: rgba(60, 63, 65, 1);
  --intellij-checkBox-disabledText: rgba(128, 128, 128, 1);
  --intellij-checkBox-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-checkBox-font-size: 13px;
  --intellij-checkBox-foreground: rgba(187, 187, 187, 1);
  --intellij-checkBox-select: rgba(255, 102, 102, 1);
  --intellij-checkBoxMenuItem-acceleratorFont-family: "Lucida Grande", system-ui,
    sans-serif;
  --intellij-checkBoxMenuItem-acceleratorFont-size: 14px;
  --intellij-checkBoxMenuItem-acceleratorForeground: rgba(0, 0, 0, 1);
  --intellij-checkBoxMenuItem-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-checkBoxMenuItem-background: rgba(60, 63, 65, 1);
  --intellij-checkBoxMenuItem-disabledBackground: rgba(255, 255, 255, 1);
  --intellij-checkBoxMenuItem-disabledForeground: rgba(128, 128, 128, 1);
  --intellij-checkBoxMenuItem-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-checkBoxMenuItem-font-size: 13px;
  --intellij-checkBoxMenuItem-foreground: rgba(187, 187, 187, 1);
  --intellij-checkBoxMenuItem-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-checkBoxMenuItem-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-code-block-borderColor: rgba(90, 93, 99, 1);
  --intellij-code-block-editorPane-background: rgba(157, 160, 168, 1);
  --intellij-code-block-editorPane-borderColor: rgba(57, 59, 64, 1);
  --intellij-code-inline-backgroundColor: rgba(30, 31, 34, 1);
  --intellij-codeWithMe-avatar-foreground: rgba(29, 29, 29, 1);
  --intellij-colorChooser-background: rgba(60, 63, 65, 1);
  --intellij-colorChooser-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-colorChooser-font-size: 13px;
  --intellij-colorChooser-foreground: rgba(187, 187, 187, 1);
  --intellij-colorChooser-swatchesDefaultRecentColor: rgba(255, 255, 255, 1);
  --intellij-comboBox-arrowButton-background: rgba(60, 63, 65, 1);
  --intellij-comboBox-arrowButton-disabledIconColor: rgba(88, 88, 88, 1);
  --intellij-comboBox-arrowButton-iconColor: rgba(154, 157, 161, 1);
  --intellij-comboBox-arrowButton-nonEditableBackground: rgba(60, 63, 65, 1);
  --intellij-comboBox-background: rgba(60, 63, 65, 1);
  --intellij-comboBox-buttonBackground: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonDarkShadow: rgba(0, 0, 0, 1);
  --intellij-comboBox-buttonHighlight: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonShadow: rgba(0, 0, 0, 0.27);
  --intellij-comboBox-disabledBackground: rgba(60, 63, 65, 1);
  --intellij-comboBox-disabledForeground: rgba(119, 119, 119, 1);
  --intellij-comboBox-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-comboBox-font-size: 13px;
  --intellij-comboBox-foreground: rgba(187, 187, 187, 1);
  --intellij-comboBox-nonEditableBackground: rgba(60, 63, 65, 1);
  --intellij-comboBox-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-comboBox-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-comboBoxButton-background: rgba(60, 63, 65, 1);
  --intellij-completionPopup-advertiser-background: rgba(69, 73, 74, 1);
  --intellij-completionPopup-advertiser-foreground: rgba(128, 128, 128, 1);
  --intellij-completionPopup-foreground: rgba(187, 187, 187, 1);
  --intellij-completionPopup-matchForeground: rgba(88, 157, 246, 1);
  --intellij-completionPopup-selectionBackground: rgba(17, 58, 92, 1);
  --intellij-completionPopup-selectionInactiveBackground: rgba(81, 84, 87, 1);
  --intellij-complexPopup-header-background: rgba(60, 63, 65, 1);
  --intellij-component-borderColor: rgba(100, 100, 100, 1);
  --intellij-component-disabledBorderColor: rgba(100, 100, 100, 1);
  --intellij-component-errorFocusColor: rgba(139, 60, 60, 1);
  --intellij-component-focusColor: rgba(61, 97, 133, 1);
  --intellij-component-focusedBorderColor: rgba(70, 109, 148, 1);
  --intellij-component-inactiveErrorFocusColor: rgba(114, 82, 82, 1);
  --intellij-component-inactiveWarningFocusColor: rgba(110, 83, 36, 1);
  --intellij-component-infoForeground: rgba(120, 120, 120, 1);
  --intellij-component-warningFocusColor: rgba(172, 121, 32, 1);
  --intellij-content-background: rgba(43, 43, 43, 1);
  --intellij-control: rgba(60, 63, 65, 1);
  --intellij-controlDkShadow: rgba(0, 0, 0, 1);
  --intellij-controlHighlight: rgba(8, 74, 217, 1);
  --intellij-controlLtHighlight: rgba(255, 255, 255, 1);
  --intellij-controlShadow: rgba(0, 0, 0, 0.27);
  --intellij-controlText: rgba(187, 187, 187, 1);
  --intellij-counter-background: rgba(60, 63, 65, 1);
  --intellij-counter-foreground: rgba(187, 187, 187, 1);
  --intellij-debugger-evaluateExpression-background: rgba(69, 73, 74, 1);
  --intellij-defaultTabs-background: rgba(60, 63, 65, 1);
  --intellij-desktop: rgba(34, 255, 6, 1);
  --intellij-desktop-background: rgba(60, 63, 65, 1);
  --intellij-desktopIcon-borderColor: rgba(0, 0, 0, 0.6);
  --intellij-desktopIcon-borderRimColor: rgba(192, 192, 192, 0.75);
  --intellij-desktopIcon-labelBackground: rgba(0, 0, 0, 0.39);
  --intellij-dragAndDrop-areaBackground: rgba(255, 255, 255, 0.2);
  --intellij-dragAndDrop-areaBorderColor: rgba(79, 115, 168, 1);
  --intellij-dragAndDrop-areaForeground: rgba(186, 186, 186, 1);
  --intellij-dragAndDrop-borderColor: rgba(47, 101, 202, 1);
  --intellij-dragAndDrop-rowBackground: rgba(47, 101, 202, 0.2);
  --intellij-editor-background: rgba(40, 40, 40, 1);
  --intellij-editor-foreground: rgba(160, 160, 160, 1);
  --intellij-editor-searchField-background: rgba(69, 73, 74, 1);
  --intellij-editor-toolTip-background: rgba(75, 77, 77, 1);
  --intellij-editor-toolTip-foreground: rgba(191, 191, 191, 1);
  --intellij-editorPane-background: rgba(60, 63, 65, 1);
  --intellij-editorPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-editorPane-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-editorPane-font-size: 13px;
  --intellij-editorPane-foreground: rgba(187, 187, 187, 1);
  --intellij-editorPane-inactiveBackground: rgba(69, 73, 74, 1);
  --intellij-editorPane-inactiveForeground: rgba(187, 187, 187, 1);
  --intellij-editorPane-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-editorPane-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-editorTabs-background: rgba(60, 63, 65, 1);
  --intellij-editorTabs-underlinedTabBackground: rgba(78, 82, 84, 1);
  --intellij-flameGraph-tooltip-foreground: rgba(187, 187, 187, 1);
  --intellij-flameGraph-tooltip-scaleBackground: rgba(54, 56, 57, 1);
  --intellij-flameGraph-tooltip-scaleColor: rgba(54, 88, 128, 1);
  --intellij-focus-color: rgba(255, 0, 0, 1);
  --intellij-formattedTextField-background: rgba(69, 73, 74, 1);
  --intellij-formattedTextField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-formattedTextField-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-formattedTextField-font-size: 13px;
  --intellij-formattedTextField-foreground: rgba(187, 187, 187, 1);
  --intellij-formattedTextField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-formattedTextField-inactiveForeground: rgba(128, 128, 128, 1);
  --intellij-formattedTextField-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-formattedTextField-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-gotItTooltip-background: rgba(60, 63, 65, 1);
  --intellij-gotItTooltip-button-foreground: rgba(187, 187, 187, 1);
  --intellij-gotItTooltip-codeBackground: rgba(76, 80, 82, 1);
  --intellij-gotItTooltip-codeBorderColor: rgba(108, 112, 126, 1);
  --intellij-gotItTooltip-foreground: rgba(191, 191, 191, 1);
  --intellij-gotItTooltip-header-foreground: rgba(187, 187, 187, 1);
  --intellij-gotItTooltip-imageBorderColor: rgba(255, 255, 255, 0.3);
  --intellij-group-separatorColor: rgba(81, 81, 81, 1);
  --intellij-helpBrowser-aiEditor-background: rgba(250, 245, 255, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-background: rgba(
    60,
    63,
    65,
    1
  );
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-foreground: rgba(
    140,
    140,
    140,
    1
  );
  --intellij-helpBrowser-userMessage-background: rgba(59, 71, 84, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-foreground: rgba(
    140,
    140,
    140,
    1
  );
  --intellij-hyperlink-linkColor: rgba(88, 157, 246, 1);
  --intellij-iconButton-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-iconButton-font-size: 11px;
  --intellij-ide-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottom1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomRight1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-left1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-right1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-top1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topLeft1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topRight1Color: rgba(0, 0, 0, 0.071);
  --intellij-inactiveCaption: rgba(57, 60, 61, 1);
  --intellij-inactiveCaptionBorder: rgba(108, 108, 108, 1);
  --intellij-inactiveCaptionText: rgba(108, 108, 108, 1);
  --intellij-info: rgba(255, 255, 255, 1);
  --intellij-infoText: rgba(187, 187, 187, 1);
  --intellij-internalFrame-activeTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-activeTitleForeground: rgba(0, 0, 0, 1);
  --intellij-internalFrame-background: rgba(60, 63, 65, 1);
  --intellij-internalFrame-borderColor: rgba(238, 238, 238, 1);
  --intellij-internalFrame-borderDarkShadow: rgba(0, 255, 0, 1);
  --intellij-internalFrame-borderHighlight: rgba(0, 0, 255, 1);
  --intellij-internalFrame-borderLight: rgba(255, 255, 0, 1);
  --intellij-internalFrame-borderShadow: rgba(255, 0, 0, 1);
  --intellij-internalFrame-inactiveTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-inactiveTitleForeground: rgba(128, 128, 128, 1);
  --intellij-internalFrame-optionDialogBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-optionDialogTitleFont-family: ".AppleSystemUIFont",
    system-ui, sans-serif;
  --intellij-internalFrame-optionDialogTitleFont-size: 14px;
  --intellij-internalFrame-paletteBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-paletteTitleFont-family: ".AppleSystemUIFont",
    system-ui, sans-serif;
  --intellij-internalFrame-paletteTitleFont-size: 14px;
  --intellij-internalFrame-titleFont-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-internalFrame-titleFont-size: 14px;
  --intellij-label-background: rgba(60, 63, 65, 1);
  --intellij-label-disabledForeground: rgba(128, 128, 128, 1);
  --intellij-label-disabledShadow: rgba(64, 64, 64, 1);
  --intellij-label-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-label-font-size: 13px;
  --intellij-label-foreground: rgba(187, 187, 187, 1);
  --intellij-label-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-lesson-badge-newLessonBackground: rgba(73, 156, 84, 1);
  --intellij-lesson-badge-newLessonForeground: rgba(254, 254, 254, 1);
  --intellij-lesson-shortcutBackground: rgba(51, 54, 56, 1);
  --intellij-lesson-stepNumberForeground: rgba(254, 254, 254, 1);
  --intellij-lineProfiler-hotLine-foreground: rgba(255, 82, 97, 1);
  --intellij-lineProfiler-hotLine-hoverBackground: rgba(112, 71, 69, 1);
  --intellij-lineProfiler-hotLine-labelBackground: rgba(89, 61, 65, 1);
  --intellij-lineProfiler-ignoredLine-foreground: rgba(95, 95, 95, 1);
  --intellij-lineProfiler-ignoredLine-labelBackground: rgba(67, 71, 74, 1);
  --intellij-lineProfiler-line-foreground: rgba(120, 120, 120, 1);
  --intellij-lineProfiler-line-hoverBackground: rgba(74, 78, 82, 1);
  --intellij-lineProfiler-line-labelBackground: rgba(67, 71, 74, 1);
  --intellij-link-activeForeground: rgba(88, 157, 246, 1);
  --intellij-link-background: rgba(60, 63, 65, 1);
  --intellij-link-foreground: rgba(187, 187, 187, 1);
  --intellij-link-hoverForeground: rgba(88, 157, 246, 1);
  --intellij-link-pressedForeground: rgba(186, 111, 37, 1);
  --intellij-link-tag-background: rgba(57, 59, 64, 1);
  --intellij-link-tag-foreground: rgba(168, 173, 189, 1);
  --intellij-link-visitedForeground: rgba(88, 157, 246, 1);
  --intellij-list-background: rgba(60, 63, 65, 1);
  --intellij-list-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-list-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-list-font-size: 13px;
  --intellij-list-foreground: rgba(187, 187, 187, 1);
  --intellij-list-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-list-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-list-selectionInactiveBackground: rgba(13, 41, 62, 1);
  --intellij-list-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-mainMenu-background: rgba(60, 63, 65, 1);
  --intellij-mainMenu-foreground: rgba(187, 187, 187, 1);
  --intellij-mainToolbar-background: rgba(60, 63, 65, 1);
  --intellij-mainToolbar-dropdown-background: rgba(60, 63, 65, 1);
  --intellij-mainToolbar-dropdown-hoverBackground: rgba(76, 80, 82, 1);
  --intellij-mainToolbar-dropdown-pressedBackground: rgba(76, 80, 82, 1);
  --intellij-mainToolbar-dropdown-transparentHoverBackground: rgba(
    255,
    255,
    255,
    0.1
  );
  --intellij-mainToolbar-foreground: rgba(187, 187, 187, 1);
  --intellij-mainToolbar-icon-background: rgba(60, 63, 65, 1);
  --intellij-mainToolbar-icon-hoverBackground: rgba(76, 80, 82, 1);
  --intellij-mainToolbar-icon-pressedBackground: rgba(76, 80, 82, 1);
  --intellij-mainWindow-fullScreeControl-background: rgba(87, 90, 92, 1);
  --intellij-mainWindow-tab-background: rgba(19, 19, 20, 1);
  --intellij-mainWindow-tab-borderColor: rgba(30, 31, 34, 1);
  --intellij-mainWindow-tab-foreground: rgba(180, 184, 191, 1);
  --intellij-mainWindow-tab-hoverBackground: rgba(26, 26, 27, 1);
  --intellij-mainWindow-tab-hoverForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedBackground: rgba(60, 63, 65, 1);
  --intellij-mainWindow-tab-selectedForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedInactiveBackground: rgba(60, 63, 65, 1);
  --intellij-mainWindow-tab-separatorColor: rgba(43, 45, 48, 1);
  --intellij-menu: rgba(255, 255, 255, 1);
  --intellij-menu-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-menu-acceleratorFont-size: 14px;
  --intellij-menu-acceleratorForeground: rgba(0, 0, 0, 1);
  --intellij-menu-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-menu-background: rgba(60, 63, 65, 1);
  --intellij-menu-borderColor: rgba(81, 81, 81, 1);
  --intellij-menu-disabledBackground: rgba(255, 255, 255, 1);
  --intellij-menu-disabledForeground: rgba(128, 128, 128, 1);
  --intellij-menu-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-menu-font-size: 13px;
  --intellij-menu-foreground: rgba(187, 187, 187, 1);
  --intellij-menu-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-menu-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-menu-separatorColor: rgba(81, 81, 81, 1);
  --intellij-menuBar-background: rgba(60, 63, 65, 1);
  --intellij-menuBar-borderColor: rgba(81, 81, 81, 1);
  --intellij-menuBar-disabledBackground: rgba(60, 63, 65, 1);
  --intellij-menuBar-disabledForeground: rgba(128, 128, 128, 1);
  --intellij-menuBar-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-menuBar-font-size: 13px;
  --intellij-menuBar-foreground: rgba(187, 187, 187, 1);
  --intellij-menuBar-highlight: rgba(255, 255, 255, 1);
  --intellij-menuBar-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-menuBar-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-menuBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-menuItem-acceleratorFont-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-menuItem-acceleratorFont-size: 13px;
  --intellij-menuItem-acceleratorForeground: rgba(188, 188, 188, 1);
  --intellij-menuItem-acceleratorSelectionForeground: rgba(187, 187, 187, 1);
  --intellij-menuItem-background: rgba(60, 63, 65, 1);
  --intellij-menuItem-disabledBackground: rgba(255, 255, 255, 1);
  --intellij-menuItem-disabledForeground: rgba(119, 119, 119, 1);
  --intellij-menuItem-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-menuItem-font-size: 13px;
  --intellij-menuItem-foreground: rgba(187, 187, 187, 1);
  --intellij-menuItem-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-menuItem-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-menuText: rgba(0, 0, 0, 0.85);
  --intellij-navBar-borderColor: rgba(81, 81, 81, 1);
  --intellij-newClass-panel-background: rgba(69, 73, 74, 1);
  --intellij-newClass-searchField-background: rgba(60, 63, 65, 1);
  --intellij-notification-background: rgba(78, 80, 82, 1);
  --intellij-notification-foreground: rgba(187, 187, 187, 1);
  --intellij-notification-moreButton-background: rgba(58, 60, 61, 1);
  --intellij-notification-moreButton-foreground: rgba(140, 140, 140, 1);
  --intellij-notification-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottom1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomRight1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-left1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-right1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-top1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topLeft1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topRight1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-welcomeScreen-separatorColor: rgba(113, 115, 117, 1);
  --intellij-optionPane-background: rgba(60, 63, 65, 1);
  --intellij-optionPane-buttonFont-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-optionPane-buttonFont-size: 13px;
  --intellij-optionPane-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-optionPane-font-size: 13px;
  --intellij-optionPane-foreground: rgba(187, 187, 187, 1);
  --intellij-optionPane-messageFont-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-optionPane-messageFont-size: 13px;
  --intellij-optionPane-messageForeground: rgba(187, 187, 187, 1);
  --intellij-packageSearch-packageTag-background: rgba(76, 78, 80, 1);
  --intellij-packageSearch-packageTag-foreground: rgba(156, 156, 156, 1);
  --intellij-packageSearch-packageTag-hoverBackground: rgba(85, 88, 91, 1);
  --intellij-packageSearch-packageTag-selectedBackground: rgba(47, 101, 202, 1);
  --intellij-packageSearch-packageTag-selectedForeground: rgba(
    187,
    187,
    187,
    1
  );
  --intellij-packageSearch-packageTagSelected-background: rgba(
    120,
    173,
    226,
    1
  );
  --intellij-packageSearch-packageTagSelected-foreground: rgba(
    187,
    187,
    187,
    1
  );
  --intellij-packageSearch-searchResult-background: rgba(63, 71, 73, 1);
  --intellij-packageSearch-searchResult-hoverBackground: rgba(70, 74, 77, 1);
  --intellij-packageSearch-searchResult-packageTag-background: rgba(
    78,
    86,
    88,
    1
  );
  --intellij-packageSearch-searchResult-packageTag-foreground: rgba(
    142,
    143,
    143,
    1
  );
  --intellij-packageSearch-searchResult-packageTag-hoverBackground: rgba(
    85,
    88,
    91,
    1
  );
  --intellij-packageSearch-searchResult-packageTag-selectedBackground: rgba(
    47,
    101,
    202,
    1
  );
  --intellij-packageSearch-searchResult-packageTag-selectedForeground: rgba(
    187,
    187,
    187,
    1
  );
  --intellij-panel-background: rgba(60, 63, 65, 1);
  --intellij-panel-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-panel-font-size: 13px;
  --intellij-panel-foreground: rgba(187, 187, 187, 1);
  --intellij-parameterInfo-background: rgba(75, 77, 77, 1);
  --intellij-parameterInfo-foreground: rgba(187, 187, 187, 1);
  --intellij-passwordField-background: rgba(69, 73, 74, 1);
  --intellij-passwordField-capsLockIconColor: rgba(0, 0, 0, 0.39);
  --intellij-passwordField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-passwordField-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-passwordField-font-size: 13px;
  --intellij-passwordField-foreground: rgba(187, 187, 187, 1);
  --intellij-passwordField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-passwordField-inactiveForeground: rgba(128, 128, 128, 1);
  --intellij-passwordField-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-passwordField-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-plugins-background: rgba(49, 51, 53, 1);
  --intellij-plugins-borderColor: rgba(81, 81, 81, 1);
  --intellij-plugins-searchField-background: rgba(49, 51, 53, 1);
  --intellij-plugins-sectionHeader-background: rgba(60, 63, 65, 1);
  --intellij-plugins-sectionHeader-foreground: rgba(153, 153, 153, 1);
  --intellij-popup-background: rgba(60, 63, 65, 1);
  --intellij-popup-borderColor: rgba(97, 97, 97, 1);
  --intellij-popup-header-activeBackground: rgba(74, 78, 82, 1);
  --intellij-popup-header-inactiveBackground: rgba(67, 70, 73, 1);
  --intellij-popup-inactiveBorderColor: rgba(86, 86, 86, 1);
  --intellij-popup-toolbar-background: rgba(60, 63, 65, 1);
  --intellij-popup-toolbar-borderColor: rgba(74, 78, 82, 1);
  --intellij-popupMenu-background: rgba(60, 63, 65, 1);
  --intellij-popupMenu-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-popupMenu-font-size: 13px;
  --intellij-popupMenu-foreground: rgba(187, 187, 187, 1);
  --intellij-popupMenu-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-popupMenu-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-popupMenu-translucentBackground: rgba(60, 63, 65, 1);
  --intellij-profiler-chartSlider-foreground: rgba(254, 254, 254, 1);
  --intellij-profiler-chartSlider-lineColor: rgba(154, 167, 176, 1);
  --intellij-profiler-cpuChart-background: rgba(98, 150, 85, 0.3);
  --intellij-profiler-cpuChart-borderColor: rgba(98, 150, 85, 1);
  --intellij-profiler-cpuChart-inactiveBackground: rgba(98, 150, 85, 0.2);
  --intellij-profiler-cpuChart-inactiveBorderColor: rgba(79, 106, 75, 1);
  --intellij-profiler-cpuChart-pointBackground: rgba(98, 150, 37, 1);
  --intellij-profiler-cpuChart-pointBorderColor: rgba(60, 63, 65, 1);
  --intellij-profiler-liveChart-horizontalAxisColor: rgba(209, 209, 209, 1);
  --intellij-profiler-memoryChart-background: rgba(88, 157, 246, 0.5);
  --intellij-profiler-memoryChart-borderColor: rgba(88, 157, 246, 1);
  --intellij-profiler-memoryChart-inactiveBackground: rgba(88, 157, 246, 0.2);
  --intellij-profiler-memoryChart-inactiveBorderColor: rgba(71, 101, 137, 1);
  --intellij-profiler-memoryChart-pointBackground: rgba(88, 157, 246, 1);
  --intellij-profiler-memoryChart-pointBorderColor: rgba(60, 63, 65, 1);
  --intellij-profiler-timer-background: rgba(50, 50, 50, 1);
  --intellij-profiler-timer-disabledForeground: rgba(119, 119, 119, 1);
  --intellij-profiler-timer-foreground: rgba(187, 187, 187, 1);
  --intellij-progressBar-background: rgba(60, 63, 65, 1);
  --intellij-progressBar-failedColor: rgba(231, 72, 72, 1);
  --intellij-progressBar-failedEndColor: rgba(244, 162, 160, 1);
  --intellij-progressBar-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-progressBar-font-size: 13px;
  --intellij-progressBar-foreground: rgba(128, 128, 128, 1);
  --intellij-progressBar-indeterminateEndColor: rgba(131, 131, 131, 1);
  --intellij-progressBar-indeterminateStartColor: rgba(105, 105, 105, 1);
  --intellij-progressBar-passedColor: rgba(0, 143, 80, 1);
  --intellij-progressBar-passedEndColor: rgba(93, 196, 143, 1);
  --intellij-progressBar-progressColor: rgba(160, 160, 160, 1);
  --intellij-progressBar-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-progressBar-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-progressBar-trackColor: rgba(85, 85, 85, 1);
  --intellij-radioButton-background: rgba(60, 63, 65, 1);
  --intellij-radioButton-darcula-selectionDisabledColor: rgba(96, 96, 96, 1);
  --intellij-radioButton-darcula-selectionDisabledShadowColor: rgba(
    60,
    60,
    60,
    1
  );
  --intellij-radioButton-darcula-selectionEnabledColor: rgba(170, 170, 170, 1);
  --intellij-radioButton-darcula-selectionEnabledShadowColor: rgba(
    30,
    30,
    30,
    1
  );
  --intellij-radioButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-radioButton-disabledText: rgba(128, 128, 128, 1);
  --intellij-radioButton-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-radioButton-font-size: 13px;
  --intellij-radioButton-foreground: rgba(187, 187, 187, 1);
  --intellij-radioButton-highlight: rgba(255, 255, 255, 1);
  --intellij-radioButton-light: rgba(8, 74, 217, 1);
  --intellij-radioButton-select: rgba(255, 102, 102, 1);
  --intellij-radioButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-radioButtonMenuItem-acceleratorFont-family: "Lucida Grande",
    system-ui, sans-serif;
  --intellij-radioButtonMenuItem-acceleratorFont-size: 14px;
  --intellij-radioButtonMenuItem-acceleratorForeground: rgba(0, 0, 0, 1);
  --intellij-radioButtonMenuItem-acceleratorSelectionForeground: rgba(
    0,
    0,
    0,
    1
  );
  --intellij-radioButtonMenuItem-background: rgba(60, 63, 65, 1);
  --intellij-radioButtonMenuItem-disabledBackground: rgba(255, 255, 255, 1);
  --intellij-radioButtonMenuItem-disabledForeground: rgba(128, 128, 128, 1);
  --intellij-radioButtonMenuItem-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-radioButtonMenuItem-font-size: 13px;
  --intellij-radioButtonMenuItem-foreground: rgba(187, 187, 187, 1);
  --intellij-radioButtonMenuItem-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-radioButtonMenuItem-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-recentProject-color1-avatar-end: rgba(233, 128, 111, 1);
  --intellij-recentProject-color1-avatar-start: rgba(224, 136, 85, 1);
  --intellij-recentProject-color1-mainToolbarGradientStart: rgba(
    101,
    75,
    64,
    1
  );
  --intellij-recentProject-color2-avatar-end: rgba(187, 127, 25, 1);
  --intellij-recentProject-color2-avatar-start: rgba(176, 139, 20, 1);
  --intellij-recentProject-color2-mainToolbarGradientStart: rgba(83, 76, 51, 1);
  --intellij-recentProject-color3-avatar-end: rgba(135, 170, 89, 1);
  --intellij-recentProject-color3-avatar-start: rgba(161, 163, 89, 1);
  --intellij-recentProject-color3-mainToolbarGradientStart: rgba(69, 80, 56, 1);
  --intellij-recentProject-color4-avatar-end: rgba(97, 131, 236, 1);
  --intellij-recentProject-color4-avatar-start: rgba(59, 146, 184, 1);
  --intellij-recentProject-color4-mainToolbarGradientStart: rgba(49, 81, 95, 1);
  --intellij-recentProject-color5-avatar-end: rgba(122, 100, 240, 1);
  --intellij-recentProject-color5-avatar-start: rgba(53, 116, 240, 1);
  --intellij-recentProject-color5-mainToolbarGradientStart: rgba(
    52,
    76,
    125,
    1
  );
  --intellij-recentProject-color6-avatar-end: rgba(169, 86, 207, 1);
  --intellij-recentProject-color6-avatar-start: rgba(200, 77, 143, 1);
  --intellij-recentProject-color6-mainToolbarGradientStart: rgba(93, 53, 74, 1);
  --intellij-recentProject-color7-avatar-end: rgba(168, 77, 224, 1);
  --intellij-recentProject-color7-avatar-start: rgba(149, 90, 224, 1);
  --intellij-recentProject-color7-mainToolbarGradientStart: rgba(
    79,
    62,
    101,
    1
  );
  --intellij-recentProject-color8-avatar-end: rgba(39, 156, 205, 1);
  --intellij-recentProject-color8-avatar-start: rgba(36, 163, 148, 1);
  --intellij-recentProject-color8-mainToolbarGradientStart: rgba(29, 71, 68, 1);
  --intellij-recentProject-color9-avatar-end: rgba(61, 150, 139, 1);
  --intellij-recentProject-color9-avatar-start: rgba(95, 173, 101, 1);
  --intellij-recentProject-color9-mainToolbarGradientStart: rgba(62, 85, 64, 1);
  --intellij-review-branch-background: rgba(69, 73, 74, 1);
  --intellij-review-branch-background-hover: rgba(82, 87, 88, 1);
  --intellij-review-chatItem-hover: rgba(75, 75, 75, 0.2);
  --intellij-review-state-background: rgba(69, 73, 74, 1);
  --intellij-review-state-foreground: rgba(140, 140, 140, 1);
  --intellij-review-timeline-thread-diff-anchorLine: rgba(84, 75, 45, 1);
  --intellij-runWidget-foreground: rgba(187, 187, 187, 1);
  --intellij-runWidget-hoverBackground: rgba(0, 0, 0, 0.098);
  --intellij-runWidget-pressedBackground: rgba(0, 0, 0, 0.16);
  --intellij-runWidget-runIconColor: rgba(95, 173, 101, 1);
  --intellij-runWidget-runningBackground: rgba(89, 158, 94, 1);
  --intellij-runWidget-stopBackground: rgba(201, 79, 79, 1);
  --intellij-screenView-defaultBorderColor: rgba(0, 0, 0, 1);
  --intellij-screenView-hoveredBorderColor: rgba(84, 138, 247, 1);
  --intellij-screenView-selectedBorderColor: rgba(84, 138, 247, 1);
  --intellij-scrollBar-background: rgba(63, 66, 68, 1);
  --intellij-scrollBar-foreground: rgba(187, 187, 187, 1);
  --intellij-scrollBar-thumb: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbDarkShadow: rgba(0, 0, 0, 1);
  --intellij-scrollBar-thumbHighlight: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbShadow: rgba(0, 0, 0, 0.27);
  --intellij-scrollBar-track: rgba(154, 154, 154, 1);
  --intellij-scrollBar-trackHighlight: rgba(0, 0, 0, 1);
  --intellij-scrollPane-background: rgba(60, 63, 65, 1);
  --intellij-scrollPane-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-scrollPane-font-size: 13px;
  --intellij-scrollPane-foreground: rgba(187, 187, 187, 1);
  --intellij-scrollbar: rgba(154, 154, 154, 1);
  --intellij-searchEverywhere-advertiser-background: rgba(69, 73, 74, 1);
  --intellij-searchEverywhere-advertiser-foreground: rgba(128, 128, 128, 1);
  --intellij-searchEverywhere-header-background: rgba(69, 73, 74, 1);
  --intellij-searchEverywhere-list-separatorColor: rgba(80, 80, 80, 1);
  --intellij-searchEverywhere-searchField-background: rgba(60, 63, 65, 1);
  --intellij-searchEverywhere-searchField-borderColor: rgba(100, 100, 100, 1);
  --intellij-searchEverywhere-searchField-infoForeground: rgba(
    128,
    128,
    128,
    1
  );
  --intellij-searchEverywhere-tab-selectedBackground: rgba(85, 90, 94, 1);
  --intellij-searchEverywhere-tab-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-searchFieldWithExtension-background: rgba(69, 73, 74, 1);
  --intellij-segmentedButton-focusedSelectedButtonColor: rgba(61, 75, 92, 1);
  --intellij-segmentedButton-selectedButtonColor: rgba(76, 80, 82, 1);
  --intellij-segmentedButton-selectedEndBorderColor: rgba(94, 96, 96, 1);
  --intellij-segmentedButton-selectedStartBorderColor: rgba(94, 96, 96, 1);
  --intellij-separator-foreground: rgba(187, 187, 187, 1);
  --intellij-separator-highlight: rgba(255, 255, 255, 1);
  --intellij-separator-separatorColor: rgba(81, 81, 81, 1);
  --intellij-separator-shadow: rgba(0, 0, 0, 0.27);
  --intellij-sidePanel-background: rgba(62, 67, 76, 1);
  --intellij-slider-background: rgba(60, 63, 65, 1);
  --intellij-slider-focus: rgba(0, 0, 0, 1);
  --intellij-slider-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-slider-font-size: 11px;
  --intellij-slider-foreground: rgba(187, 187, 187, 1);
  --intellij-slider-highlight: rgba(255, 255, 255, 1);
  --intellij-slider-shadow: rgba(0, 0, 0, 0.27);
  --intellij-slider-tickColor: rgba(102, 102, 102, 1);
  --intellij-space-review-acceptedOutline: rgba(84, 181, 118, 1);
  --intellij-space-review-waitForResponseOutline: rgba(140, 211, 236, 1);
  --intellij-space-review-workingOutline: rgba(255, 148, 102, 1);
  --intellij-speedSearch-background: rgba(69, 73, 74, 1);
  --intellij-speedSearch-borderColor: rgba(64, 64, 64, 1);
  --intellij-speedSearch-errorForeground: rgba(255, 100, 100, 1);
  --intellij-speedSearch-foreground: rgba(187, 187, 187, 1);
  --intellij-spinner-background: rgba(60, 63, 65, 1);
  --intellij-spinner-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-spinner-font-size: 13px;
  --intellij-spinner-foreground: rgba(187, 187, 187, 1);
  --intellij-splitPane-background: rgba(60, 63, 65, 1);
  --intellij-splitPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-splitPane-highlight: rgba(60, 63, 65, 1);
  --intellij-splitPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-splitPaneDivider-draggingColor: rgba(64, 64, 64, 1);
  --intellij-statusBar-borderColor: rgba(70, 70, 70, 1);
  --intellij-statusBar-breadcrumbs-foreground: rgba(187, 187, 187, 1);
  --intellij-statusBar-hoverBackground: rgba(76, 80, 82, 1);
  --intellij-statusBar-lightEditBackground: rgba(47, 71, 94, 1);
  --intellij-statusBar-widget-foreground: rgba(187, 187, 187, 1);
  --intellij-tabbedPane-background: rgba(60, 63, 65, 1);
  --intellij-tabbedPane-contentAreaColor: rgba(50, 50, 50, 1);
  --intellij-tabbedPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-disabledForeground: rgba(119, 119, 119, 1);
  --intellij-tabbedPane-disabledUnderlineColor: rgba(122, 122, 122, 1);
  --intellij-tabbedPane-focus: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-focusColor: rgba(61, 75, 92, 1);
  --intellij-tabbedPane-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-tabbedPane-font-size: 13px;
  --intellij-tabbedPane-foreground: rgba(187, 187, 187, 1);
  --intellij-tabbedPane-highlight: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-hoverColor: rgba(46, 49, 51, 1);
  --intellij-tabbedPane-light: rgba(8, 74, 217, 1);
  --intellij-tabbedPane-nonSelectedTabTitleNormalColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleDisabledColor: rgba(
    255,
    255,
    255,
    0.55
  );
  --intellij-tabbedPane-selectedTabTitleNormalColor: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-selectedTabTitlePressedColor: rgba(240, 240, 240, 1);
  --intellij-tabbedPane-selectedTabTitleShadowDisabledColor: rgba(
    0,
    0,
    0,
    0.25
  );
  --intellij-tabbedPane-selectedTabTitleShadowNormalColor: rgba(0, 0, 0, 0.39);
  --intellij-tabbedPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-tabbedPane-smallFont-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-tabbedPane-smallFont-size: 11px;
  --intellij-tabbedPane-underlineColor: rgba(74, 136, 199, 1);
  --intellij-table-background: rgba(60, 63, 65, 1);
  --intellij-table-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-table-dropLineShortColor: rgba(0, 0, 0, 1);
  --intellij-table-focusCellBackground: rgba(0, 0, 0, 1);
  --intellij-table-focusCellForeground: rgba(165, 205, 255, 1);
  --intellij-table-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-table-font-size: 13px;
  --intellij-table-foreground: rgba(187, 187, 187, 1);
  --intellij-table-gridColor: rgba(79, 81, 82, 1);
  --intellij-table-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-table-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-table-selectionInactiveBackground: rgba(13, 41, 62, 1);
  --intellij-table-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-table-sortIconColor: rgba(0, 0, 0, 0.27);
  --intellij-tableHeader-background: rgba(69, 72, 74, 1);
  --intellij-tableHeader-bottomSeparatorColor: rgba(51, 54, 56, 1);
  --intellij-tableHeader-focusCellBackground: rgba(255, 255, 255, 1);
  --intellij-tableHeader-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-tableHeader-font-size: 11px;
  --intellij-tableHeader-foreground: rgba(187, 187, 187, 1);
  --intellij-tableHeader-separatorColor: rgba(51, 54, 56, 1);
  --intellij-tag-background: rgba(76, 80, 82, 1);
  --intellij-text: rgba(187, 187, 187, 1);
  --intellij-textArea-background: rgba(69, 73, 74, 1);
  --intellij-textArea-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textArea-font-family: "Monospaced", system-ui, sans-serif;
  --intellij-textArea-font-size: 13px;
  --intellij-textArea-foreground: rgba(187, 187, 187, 1);
  --intellij-textArea-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textArea-inactiveForeground: rgba(128, 128, 128, 1);
  --intellij-textArea-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-textArea-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-textComponent-selectionBackgroundInactive: rgba(13, 41, 62, 1);
  --intellij-textField-background: rgba(69, 73, 74, 1);
  --intellij-textField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textField-darkShadow: rgba(0, 0, 0, 1);
  --intellij-textField-disabledBackground: rgba(60, 63, 65, 1);
  --intellij-textField-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-textField-font-size: 13px;
  --intellij-textField-foreground: rgba(187, 187, 187, 1);
  --intellij-textField-highlight: rgba(255, 255, 255, 1);
  --intellij-textField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textField-inactiveForeground: rgba(128, 128, 128, 1);
  --intellij-textField-light: rgba(8, 74, 217, 1);
  --intellij-textField-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-textField-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-textField-shadow: rgba(0, 0, 0, 0.27);
  --intellij-textHighlight: rgba(165, 205, 255, 1);
  --intellij-textHighlightText: rgba(0, 0, 0, 1);
  --intellij-textInactiveText: rgba(128, 128, 128, 1);
  --intellij-textPane-background: rgba(60, 63, 65, 1);
  --intellij-textPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textPane-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-textPane-font-size: 13px;
  --intellij-textPane-foreground: rgba(187, 187, 187, 1);
  --intellij-textPane-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textPane-inactiveForeground: rgba(128, 128, 128, 1);
  --intellij-textPane-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-textPane-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-textText: rgba(187, 187, 187, 1);
  --intellij-tipOfTheDay-image-borderColor: rgba(50, 50, 50, 1);
  --intellij-titledBorder-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-titledBorder-font-size: 13px;
  --intellij-titledBorder-titleColor: rgba(187, 187, 187, 1);
  --intellij-toggleButton-background: rgba(60, 63, 65, 1);
  --intellij-toggleButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toggleButton-disabledText: rgba(128, 128, 128, 1);
  --intellij-toggleButton-font-family: ".AppleSystemUIFont", system-ui,
    sans-serif;
  --intellij-toggleButton-font-size: 13px;
  --intellij-toggleButton-foreground: rgba(187, 187, 187, 1);
  --intellij-toggleButton-highlight: rgba(255, 255, 255, 1);
  --intellij-toggleButton-light: rgba(8, 74, 217, 1);
  --intellij-toggleButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolBar-background: rgba(60, 63, 65, 1);
  --intellij-toolBar-borderHandleColor: rgba(140, 140, 140, 1);
  --intellij-toolBar-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toolBar-dockingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-dockingForeground: rgba(8, 74, 217, 1);
  --intellij-toolBar-floatingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-floatingForeground: rgba(64, 64, 64, 1);
  --intellij-toolBar-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-toolBar-font-size: 13px;
  --intellij-toolBar-foreground: rgba(187, 187, 187, 1);
  --intellij-toolBar-highlight: rgba(255, 255, 255, 1);
  --intellij-toolBar-light: rgba(8, 74, 217, 1);
  --intellij-toolBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolTip-background: rgba(75, 77, 77, 1);
  --intellij-toolTip-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-toolTip-font-size: 13px;
  --intellij-toolTip-foreground: rgba(191, 191, 191, 1);
  --intellij-toolTip-infoForeground: rgba(145, 145, 145, 1);
  --intellij-toolWindow-header-background: rgba(59, 71, 84, 1);
  --intellij-toolWindow-header-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-toolWindow-headerCloseButton-background: rgba(63, 65, 70, 1);
  --intellij-toolWindow-headerTab-selectedBackground: rgba(49, 59, 69, 1);
  --intellij-toolWindow-headerTab-selectedInactiveBackground: rgba(
    52,
    54,
    56,
    1
  );
  --intellij-toolbar-floating-background: rgba(69, 74, 77, 1);
  --intellij-tooltip-actions-background: rgba(67, 71, 74, 1);
  --intellij-tooltip-background: rgba(60, 63, 65, 1);
  --intellij-tooltip-foreground: rgba(191, 191, 191, 1);
  --intellij-tooltip-learning-background: rgba(14, 98, 207, 1);
  --intellij-tooltip-learning-borderColor: rgba(14, 98, 207, 1);
  --intellij-tooltip-learning-foreground: rgba(245, 245, 245, 1);
  --intellij-tooltip-learning-secondaryActionForeground: rgba(106, 157, 222, 1);
  --intellij-tooltip-learning-spanBackground: rgba(2, 80, 176, 1);
  --intellij-tooltip-learning-spanForeground: rgba(245, 245, 245, 1);
  --intellij-tooltip-learning-stepNumberForeground: rgba(106, 157, 222, 1);
  --intellij-tree-background: rgba(60, 63, 65, 1);
  --intellij-tree-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-tree-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-tree-font-size: 13px;
  --intellij-tree-foreground: rgba(187, 187, 187, 1);
  --intellij-tree-hash: rgba(80, 83, 85, 1);
  --intellij-tree-line: rgba(255, 255, 255, 1);
  --intellij-tree-selectionBackground: rgba(47, 101, 202, 1);
  --intellij-tree-selectionBorderColor: rgba(8, 74, 217, 1);
  --intellij-tree-selectionForeground: rgba(187, 187, 187, 1);
  --intellij-tree-selectionInactiveBackground: rgba(13, 41, 62, 1);
  --intellij-tree-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-tree-textBackground: rgba(60, 63, 65, 1);
  --intellij-tree-textForeground: rgba(187, 187, 187, 1);
  --intellij-uiDesigner-activity-borderColor: rgba(45, 47, 49, 1);
  --intellij-uiDesigner-canvas-background: rgba(45, 47, 49, 1);
  --intellij-uiDesigner-colorPicker-background: rgba(64, 64, 64, 1);
  --intellij-uiDesigner-colorPicker-foreground: rgba(64, 64, 64, 1);
  --intellij-uiDesigner-component-background: rgba(81, 86, 88, 1);
  --intellij-uiDesigner-component-borderColor: rgba(45, 47, 49, 1);
  --intellij-uiDesigner-component-foreground: rgba(136, 136, 136, 1);
  --intellij-uiDesigner-component-hoverBorderColor: rgba(161, 161, 161, 1);
  --intellij-uiDesigner-connector-borderColor: rgba(136, 136, 136, 1);
  --intellij-uiDesigner-connector-hoverBorderColor: rgba(136, 136, 136, 1);
  --intellij-uiDesigner-highStroke-foreground: rgba(176, 176, 176, 1);
  --intellij-uiDesigner-label-foreground: rgba(186, 186, 187, 1);
  --intellij-uiDesigner-list-selectionBackground: rgba(54, 57, 58, 1);
  --intellij-uiDesigner-panel-background: rgba(49, 52, 53, 1);
  --intellij-uiDesigner-percent-foreground: rgba(64, 64, 64, 1);
  --intellij-uiDesigner-placeholder-background: rgba(81, 86, 88, 1);
  --intellij-uiDesigner-placeholder-borderColor: rgba(63, 66, 68, 1);
  --intellij-uiDesigner-placeholder-foreground: rgba(136, 136, 136, 1);
  --intellij-uiDesigner-placeholder-selectedForeground: rgba(156, 205, 255, 1);
  --intellij-validationTooltip-errorBackground: rgba(89, 61, 65, 1);
  --intellij-validationTooltip-errorBorderColor: rgba(115, 69, 75, 1);
  --intellij-validationTooltip-warningBackground: rgba(89, 78, 50, 1);
  --intellij-validationTooltip-warningBorderColor: rgba(120, 85, 11, 1);
  --intellij-versionControl-fileHistory-commit-selectedBranchBackground: rgba(
    73,
    73,
    63,
    1
  );
  --intellij-versionControl-hgLog-bookmarkIconColor: rgba(159, 121, 181, 1);
  --intellij-versionControl-hgLog-branchIconColor: rgba(60, 180, 92, 1);
  --intellij-versionControl-hgLog-closedBranchIconColor: rgba(255, 95, 111, 1);
  --intellij-versionControl-hgLog-headIconColor: rgba(195, 30, 140, 1);
  --intellij-versionControl-hgLog-localTagIconColor: rgba(0, 243, 243, 1);
  --intellij-versionControl-hgLog-mqTagIconColor: rgba(0, 85, 255, 1);
  --intellij-versionControl-hgLog-tagIconColor: rgba(153, 153, 153, 1);
  --intellij-versionControl-hgLog-tipIconColor: rgba(225, 199, 49, 1);
  --intellij-versionControl-log-commit-currentBranchBackground: rgba(
    63,
    71,
    73,
    1
  );
  --intellij-versionControl-log-commit-reference-foreground: rgba(
    144,
    144,
    144,
    1
  );
  --intellij-versionControl-log-commit-unmatchedForeground: rgba(96, 96, 96, 1);
  --intellij-versionControl-refLabel-foreground: rgba(144, 144, 144, 1);
  --intellij-viewport-background: rgba(60, 63, 65, 1);
  --intellij-viewport-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-viewport-font-size: 13px;
  --intellij-viewport-foreground: rgba(187, 187, 187, 1);
  --intellij-welcomeScreen-background: rgba(69, 71, 74, 1);
  --intellij-welcomeScreen-details-background: rgba(49, 51, 53, 1);
  --intellij-welcomeScreen-projects-actions-background: rgba(60, 92, 134, 1);
  --intellij-welcomeScreen-projects-actions-selectionBackground: rgba(
    50,
    111,
    193,
    1
  );
  --intellij-welcomeScreen-projects-actions-selectionBorderColor: rgba(
    53,
    116,
    240,
    1
  );
  --intellij-welcomeScreen-sidePanel-background: rgba(60, 63, 65, 1);
  --intellij-window: rgba(60, 63, 65, 1);
  --intellij-windowBorder: rgba(154, 154, 154, 1);
  --intellij-windowText: rgba(0, 0, 0, 0.85);
}

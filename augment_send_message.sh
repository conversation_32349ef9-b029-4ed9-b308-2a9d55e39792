#!/bin/bash
# Augment AI 直接消息发送脚本
# 绕过UI界面直接与Augment AI交互

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 发现IntelliJ IDEA HTTP API端口
discover_port() {
    print_info "正在查找IntelliJ IDEA HTTP API端口..."
    
    # 常用端口
    local ports=(63342 63343 63344 63345)
    
    for port in "${ports[@]}"; do
        if command -v netstat >/dev/null 2>&1; then
            # Windows/Linux netstat
            if netstat -an 2>/dev/null | grep -q ":$port.*LISTEN"; then
                print_success "发现端口: $port"
                echo "$port"
                return 0
            fi
        elif command -v lsof >/dev/null 2>&1; then
            # Mac/Linux lsof
            if lsof -i ":$port" 2>/dev/null | grep -q "java"; then
                print_success "发现端口: $port"
                echo "$port"
                return 0
            fi
        fi
    done
    
    print_warning "未找到活跃端口，使用默认端口: 63342"
    echo "63342"
}

# 测试API连接
test_connection() {
    local base_url="$1"
    print_info "测试API连接: $base_url"
    
    local endpoints=("/api/about" "/api" "/")
    
    for endpoint in "${endpoints[@]}"; do
        if curl -s --connect-timeout 5 "$base_url$endpoint" >/dev/null 2>&1; then
            print_success "API连接成功"
            return 0
        fi
    done
    
    print_error "API连接失败"
    return 1
}

# 发送消息 - 方法1：直接API
send_message_direct() {
    local base_url="$1"
    local message="$2"
    local model_id="${3:-default}"
    
    print_info "方法1: 直接API发送消息"
    
    local endpoints=(
        "/api/augment/chat/direct"
        "/api/augment/chat"
        "/api/chat/message"
        "/api/chat"
    )
    
    local payload=$(cat <<EOF
{
    "text": "$message",
    "chatHistory": [],
    "modelId": "$model_id",
    "disableRetrieval": false,
    "silent": false
}
EOF
)
    
    for endpoint in "${endpoints[@]}"; do
        print_info "尝试端点: $endpoint"
        
        local response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$base_url$endpoint" 2>/dev/null)
        
        local http_code=$(echo "$response" | tail -n1)
        local body=$(echo "$response" | head -n -1)
        
        if [[ "$http_code" == "200" ]]; then
            print_success "消息发送成功！"
            echo "响应: $body"
            return 0
        else
            print_warning "端点响应: $http_code"
            [[ -n "$body" ]] && echo "响应内容: $body"
        fi
    done
    
    return 1
}

# 发送消息 - 方法2：异步API
send_message_async() {
    local base_url="$1"
    local message="$2"
    
    print_info "方法2: 异步API发送消息"
    
    local request_id=$(date +%s%3N)
    local endpoints=(
        "/api/messaging/async"
        "/api/webview/message"
        "/api/augment/async"
    )
    
    local payload=$(cat <<EOF
{
    "requestId": "$request_id",
    "baseMsg": {
        "@type": "type.googleapis.com/ChatUserMessageRequest",
        "data": {
            "text": "$message",
            "chatHistory": [],
            "modelId": "default"
        }
    }
}
EOF
)
    
    for endpoint in "${endpoints[@]}"; do
        print_info "尝试异步端点: $endpoint"
        
        local response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$base_url$endpoint" 2>/dev/null)
        
        local http_code=$(echo "$response" | tail -n1)
        local body=$(echo "$response" | head -n -1)
        
        if [[ "$http_code" == "200" ]]; then
            print_success "异步消息发送成功！"
            echo "响应: $body"
            return 0
        else
            print_warning "异步端点响应: $http_code"
        fi
    done
    
    return 1
}

# 发送消息 - 方法3：WebView消息
send_message_webview() {
    local base_url="$1"
    local message="$2"
    
    print_info "方法3: WebView消息发送"
    
    local endpoints=(
        "/api/webview/chat"
        "/api/webview/message"
        "/api/ide/webview"
    )
    
    local payload=$(cat <<EOF
{
    "type": "chat-user-message",
    "data": {
        "text": "$message",
        "chatHistory": [],
        "modelId": "default",
        "disableRetrieval": false
    }
}
EOF
)
    
    for endpoint in "${endpoints[@]}"; do
        print_info "尝试WebView端点: $endpoint"
        
        local response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$base_url$endpoint" 2>/dev/null)
        
        local http_code=$(echo "$response" | tail -n1)
        local body=$(echo "$response" | head -n -1)
        
        if [[ "$http_code" == "200" ]]; then
            print_success "WebView消息发送成功！"
            echo "响应: $body"
            return 0
        else
            print_warning "WebView端点响应: $http_code"
        fi
    done
    
    return 1
}

# 主函数
main() {
    echo "🤖 Augment AI 直接消息发送工具"
    echo "=================================="
    
    # 获取消息参数
    local message="$1"
    if [[ -z "$message" ]]; then
        echo "用法: $0 \"你的消息内容\""
        echo "示例: $0 \"解释这个项目的结构\""
        exit 1
    fi
    
    # 发现端口
    local port=$(discover_port)
    local base_url="http://localhost:$port"
    
    # 测试连接
    if ! test_connection "$base_url"; then
        print_error "无法连接到Augment API，请确保："
        echo "  1. IntelliJ IDEA正在运行"
        echo "  2. Augment插件已安装并启用"
        echo "  3. 防火墙允许本地连接"
        exit 1
    fi
    
    echo ""
    echo "📤 发送消息: $message"
    echo "=================================="
    
    # 尝试所有方法
    if send_message_direct "$base_url" "$message"; then
        exit 0
    fi
    
    echo ""
    echo "--------------------------------"
    echo ""
    
    if send_message_async "$base_url" "$message"; then
        exit 0
    fi
    
    echo ""
    echo "--------------------------------"
    echo ""
    
    if send_message_webview "$base_url" "$message"; then
        exit 0
    fi
    
    echo ""
    print_error "所有方法都失败了！"
    echo ""
    echo "🔧 调试建议："
    echo "  1. 检查IntelliJ IDEA日志"
    echo "  2. 确认Augment插件版本"
    echo "  3. 尝试重启IDE"
    echo "  4. 检查网络防火墙设置"
    
    exit 1
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

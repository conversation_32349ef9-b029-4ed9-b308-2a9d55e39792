import{S as Q,i as W,s as k,d as f,t as d,q as p,o as R,p as S,c as m,N as U,n as T,D as g,E as h,G as y,a3 as D,a4 as q,a5 as I,a6 as F,V as N,Y as E,F as H,h as X,a7 as j,X as Y}from"./SpinnerAugment-BejxPZX4.js";import{e as _}from"./IconButtonAugment-DmfIVAYG.js";import{D as v,A as V}from"./index-LNVO_dOZ.js";import{B as z}from"./ButtonAugment-CVatBELe.js";import{C as J}from"./chevron-down-D78W1-g3.js";import{T as K}from"./CardAugment-dQY7gB6x.js";import{R as A}from"./message-broker-B_Xbv83k.js";function G(r,e,n){const t=r.slice();return t[17]=e[n],t}function O(r){let e,n,t,s;function o(l){r[12](l)}function i(l){r[13](l)}let c={$$slots:{default:[rt]},$$scope:{ctx:r}};return r[1]!==void 0&&(c.requestClose=r[1]),r[0]!==void 0&&(c.focusedIndex=r[0]),e=new v.Root({props:c}),D.push(()=>q(e,"requestClose",o)),D.push(()=>q(e,"focusedIndex",i)),{c(){y(e.$$.fragment)},m(l,a){h(e,l,a),s=!0},p(l,a){const $={};1048596&a&&($.$$scope={dirty:a,ctx:l}),!n&&2&a&&(n=!0,$.requestClose=l[1],I(()=>n=!1)),!t&&1&a&&(t=!0,$.focusedIndex=l[0],I(()=>t=!1)),e.$set($)},i(l){s||(p(e.$$.fragment,l),s=!0)},o(l){d(e.$$.fragment,l),s=!1},d(l){g(e,l)}}}function P(r){let e,n;return e=new K({props:{content:"Workspace guidelines are always applied",$$slots:{default:[at]},$$scope:{ctx:r}}}),{c(){y(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){const o={};1048576&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){g(e,t)}}}function Z(r){let e,n=r[2].label+"";return{c(){e=E(n)},m(t,s){m(t,e,s)},p(t,s){4&s&&n!==(n=t[2].label+"")&&Y(e,n)},d(t){t&&f(e)}}}function tt(r){let e,n;return e=new J({props:{slot:"iconRight"}}),{c(){y(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p:T,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){g(e,t)}}}function et(r){let e,n,t;return n=new z({props:{color:"neutral",size:1,variant:"soft",$$slots:{iconRight:[tt],default:[Z]},$$scope:{ctx:r}}}),{c(){e=H("div"),y(n.$$.fragment),X(e,"class","c-dropdown-label svelte-9n7h82")},m(s,o){m(s,e,o),h(n,e,null),t=!0},p(s,o){const i={};1048580&o&&(i.$$scope={dirty:o,ctx:s}),n.$set(i)},i(s){t||(p(n.$$.fragment,s),t=!0)},o(s){d(n.$$.fragment,s),t=!1},d(s){s&&f(e),g(n)}}}function nt(r){let e,n=r[17].label+"";return{c(){e=E(n)},m(t,s){m(t,e,s)},p:T,d(t){t&&f(e)}}}function L(r){let e,n;return e=new v.Item({props:{onSelect:function(){return r[11](r[17])},highlight:r[2].label===r[17].label,$$slots:{default:[nt]},$$scope:{ctx:r}}}),{c(){y(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){r=t;const o={};4&s&&(o.highlight=r[2].label===r[17].label),1048576&s&&(o.$$scope={dirty:s,ctx:r}),e.$set(o)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){g(e,t)}}}function M(r){let e,n,t,s;return e=new v.Separator({}),t=new v.Label({props:{$$slots:{default:[ot]},$$scope:{ctx:r}}}),{c(){y(e.$$.fragment),n=N(),y(t.$$.fragment)},m(o,i){h(e,o,i),m(o,n,i),h(t,o,i),s=!0},p(o,i){const c={};1048596&i&&(c.$$scope={dirty:i,ctx:o}),t.$set(c)},i(o){s||(p(e.$$.fragment,o),p(t.$$.fragment,o),s=!0)},o(o){d(e.$$.fragment,o),d(t.$$.fragment,o),s=!1},d(o){o&&f(n),g(e,o),g(t,o)}}}function ot(r){let e,n=(r[4]!==void 0?r[5][r[4]].description:r[2].description)+"";return{c(){e=E(n)},m(t,s){m(t,e,s)},p(t,s){20&s&&n!==(n=(t[4]!==void 0?t[5][t[4]].description:t[2].description)+"")&&Y(e,n)},d(t){t&&f(e)}}}function st(r){let e,n,t,s=_(r[5]),o=[];for(let l=0;l<s.length;l+=1)o[l]=L(G(r,s,l));const i=l=>d(o[l],1,1,()=>{o[l]=null});let c=(r[4]!==void 0||r[2])&&M(r);return{c(){for(let l=0;l<o.length;l+=1)o[l].c();e=N(),c&&c.c(),n=U()},m(l,a){for(let $=0;$<o.length;$+=1)o[$]&&o[$].m(l,a);m(l,e,a),c&&c.m(l,a),m(l,n,a),t=!0},p(l,a){if(100&a){let $;for(s=_(l[5]),$=0;$<s.length;$+=1){const x=G(l,s,$);o[$]?(o[$].p(x,a),p(o[$],1)):(o[$]=L(x),o[$].c(),p(o[$],1),o[$].m(e.parentNode,e))}for(R(),$=s.length;$<o.length;$+=1)i($);S()}l[4]!==void 0||l[2]?c?(c.p(l,a),20&a&&p(c,1)):(c=M(l),c.c(),p(c,1),c.m(n.parentNode,n)):c&&(R(),d(c,1,1,()=>{c=null}),S())},i(l){if(!t){for(let a=0;a<s.length;a+=1)p(o[a]);p(c),t=!0}},o(l){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)d(o[a]);d(c),t=!1},d(l){l&&(f(e),f(n)),j(o,l),c&&c.d(l)}}}function rt(r){let e,n,t,s;return e=new v.Trigger({props:{$$slots:{default:[et]},$$scope:{ctx:r}}}),t=new v.Content({props:{side:"bottom",align:"start",$$slots:{default:[st]},$$scope:{ctx:r}}}),{c(){y(e.$$.fragment),n=N(),y(t.$$.fragment)},m(o,i){h(e,o,i),m(o,n,i),h(t,o,i),s=!0},p(o,i){const c={};1048580&i&&(c.$$scope={dirty:i,ctx:o}),e.$set(c);const l={};1048596&i&&(l.$$scope={dirty:i,ctx:o}),t.$set(l)},i(o){s||(p(e.$$.fragment,o),p(t.$$.fragment,o),s=!0)},o(o){d(e.$$.fragment,o),d(t.$$.fragment,o),s=!1},d(o){o&&f(n),g(e,o),g(t,o)}}}function lt(r){let e;return{c(){e=E("Always")},m(n,t){m(n,e,t)},d(n){n&&f(e)}}}function at(r){let e,n;return e=new z({props:{color:"accent",size:1,disabled:!0,$$slots:{default:[lt]},$$scope:{ctx:r}}}),{c(){y(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){const o={};1048576&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){g(e,t)}}}function ct(r){let e,n,t,s,o;const i=[P,O],c=[];function l(a,$){return 8&$&&(e=null),e==null&&(e=!!a[7](a[3])),e?0:1}return n=l(r,-1),t=c[n]=i[n](r),{c(){t.c(),s=U()},m(a,$){c[n].m(a,$),m(a,s,$),o=!0},p(a,[$]){let x=n;n=l(a,$),n===x?c[n].p(a,$):(R(),d(c[x],1,1,()=>{c[x]=null}),S(),t=c[n],t?t.p(a,$):(t=c[n]=i[n](a),t.c()),p(t,1),t.m(s.parentNode,s))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&f(s),c[n].d(a)}}}function $t(r,e,n){let t,s,o,i,c=T,l=()=>(c(),c=F(w,u=>n(4,i=u)),w);r.$$.on_destroy.push(()=>c());let{onSave:a}=e,{rule:$}=e;const x=[{label:"Always",value:A.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:A.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:A.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let w;l();let b=()=>{};async function C(u){b();try{await a(u.value,u.value!==A.AGENT_REQUESTED||$.description?$.description:"Example description")}catch(B){console.error("RulesModeSelector: Error in onSave:",B)}}return r.$$set=u=>{"onSave"in u&&n(8,a=u.onSave),"rule"in u&&n(9,$=u.rule)},r.$$.update=()=>{512&r.$$.dirty&&n(3,t=$.path),512&r.$$.dirty&&n(10,s=$.type),1024&r.$$.dirty&&n(2,o=x.find(u=>u.value===s))},[w,b,o,t,i,x,C,function(u){return u===V},a,$,s,u=>C(u),function(u){b=u,n(1,b)},function(u){w=u,l(n(0,w))}]}class ht extends Q{constructor(e){super(),W(this,e,$t,ct,k,{onSave:8,rule:9})}}export{ht as R};

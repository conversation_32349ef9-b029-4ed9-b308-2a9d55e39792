var B=Object.defineProperty;var E=(a,t,e)=>t in a?B(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e;var A=(a,t,e)=>E(a,typeof t!="symbol"?t+"":t,e);import{W as f}from"./IconButtonAugment-DmfIVAYG.js";import{S as C,i as x,s as b,a as m,n as c,d as S,b as M,g as V,u as L,v as k,w as W,x as Z,f as p,H as F,j as _,c as H,e as u,h as s,$ as I,X as U,a0 as $,a1 as w,F as y,V as O,Y as j}from"./SpinnerAugment-BejxPZX4.js";import{g as z,a as J}from"./remote-agents-client-DwCOg82s.js";const Q="remoteAgentStore",tt="remoteAgentStore";function et(a){const t=a;return Array.isArray(t==null?void 0:t.agentOverviews)&&Array.isArray(t==null?void 0:t.activeWebviews)&&((t==null?void 0:t.pinnedAgents)===void 0||typeof t.pinnedAgents=="object")}class st{constructor(t,e=void 0,r,n){A(this,"subscribers",new Set);this._msgBroker=t,this._state=e,this.validateState=r,this._storeId=n,e&&this.setStateInternal(e)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,e){return t.id===this.storeId&&this.validateState(e)}update(t){const e=t(this._state);e!==void 0&&this.setStateInternal(e)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:f.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:f.getSharedWebviewState,id:this.storeId,data:{}});t.type===f.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case f.updateSharedWebviewState:case f.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}function N(a){let t,e,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},a[0]],n={};for(let i=0;i<r.length;i+=1)n=m(n,r[i]);return{c(){t=p("svg"),e=new F(!0),this.h()},l(i){t=k(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=W(t);e=Z(o,!0),o.forEach(S),this.h()},h(){e.a=null,M(t,n)},m(i,o){L(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m134.6 51.7-10.8 140.9c-1.1 14.6-8.8 27.8-20.9 36-23.9 16.2-41.8 40.8-49.1 70.3l-1.3 5.1H168v-88c0-13.3 10.7-24 24-24s24 10.7 24 24v88h115.5l-1.3-5.1c-7.4-29.5-25.2-54.1-49.1-70.2-12.1-8.2-19.8-21.5-20.9-36l-10.8-141c-.1-1.2-.1-2.5-.1-3.7H134.8c0 1.2 0 2.5-.1 3.7zM168 352H32c-9.9 0-19.2-4.5-25.2-12.3s-8.2-17.9-5.8-27.5l6.2-25c10.3-41.3 35.4-75.7 68.7-98.3L83.1 96l3.7-48H56c-4.4 0-8.6-1.2-12.2-3.3C36.8 40.5 32 32.8 32 24 32 10.7 42.7 0 56 0h272c13.3 0 24 10.7 24 24 0 8.8-4.8 16.5-11.8 20.7-3.6 2.1-7.7 3.3-12.2 3.3h-30.8l3.7 48 7.1 92.9c33.3 22.6 58.4 57.1 68.7 98.3l6.2 25c2.4 9.6.2 19.7-5.8 27.5S361.7 352 351.9 352h-136v136c0 13.3-10.7 24-24 24s-24-10.7-24-24V352z"/>',t)},p(i,[o]){M(t,n=V(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&i[0]]))},i:c,o:c,d(i){i&&S(t)}}}function R(a,t,e){return a.$$set=r=>{e(0,t=m(m({},t),_(r)))},[t=_(t)]}class at extends C{constructor(t){super(),x(this,t,R,N,b,{})}}function P(a){let t,e;return{c(){t=p("svg"),e=p("path"),s(e,"fill-rule","evenodd"),s(e,"clip-rule","evenodd"),s(e,"d","M5.5 1C5.22386 1 5 1.22386 5 1.5C5 1.77614 5.22386 2 5.5 2H9.5C9.77614 2 10 1.77614 10 1.5C10 1.22386 9.77614 1 9.5 1H5.5ZM3 3.5C3 3.22386 3.22386 3 3.5 3H5H10H11.5C11.7761 3 12 3.22386 12 3.5C12 3.77614 11.7761 4 11.5 4H11V12C11 12.5523 10.5523 13 10 13H5C4.44772 13 4 12.5523 4 12V4L3.5 4C3.22386 4 3 3.77614 3 3.5ZM5 4H10V12H5V4Z"),s(e,"fill","currentColor"),s(t,"width","15"),s(t,"height","15"),s(t,"viewBox","0 0 15 15"),s(t,"fill","none"),s(t,"xmlns","http://www.w3.org/2000/svg")},m(r,n){H(r,t,n),u(t,e)},p:c,i:c,o:c,d(r){r&&S(t)}}}class it extends C{constructor(t){super(),x(this,t,null,P,b,{})}}function T(a){let t,e,r,n,i;return{c(){t=p("svg"),e=p("rect"),r=p("path"),n=p("path"),i=p("path"),s(e,"width","16"),s(e,"height","16"),s(e,"fill","currentColor"),s(e,"fill-opacity","0.01"),s(r,"fill-rule","evenodd"),s(r,"clip-rule","evenodd"),s(r,"d","M3.4718 3.46066C3.65925 3.2732 3.96317 3.2732 4.15062 3.46066L7.35062 6.66066C7.44064 6.75068 7.49121 6.87277 7.49121 7.00007C7.49121 7.12737 7.44064 7.24946 7.35062 7.33949L4.15062 10.5395C3.96317 10.7269 3.65925 10.7269 3.4718 10.5395C3.28435 10.352 3.28435 10.0481 3.4718 9.86067L6.33239 7.00007L3.4718 4.13949C3.28435 3.95203 3.28435 3.64812 3.4718 3.46066Z"),s(r,"fill","currentColor"),s(n,"fill-rule","evenodd"),s(n,"clip-rule","evenodd"),s(n,"d","M7.86854 10.6132C7.57399 10.6132 7.33521 10.8519 7.33521 11.1465C7.33521 11.441 7.57399 11.6798 7.86854 11.6798H12.1352C12.4298 11.6798 12.6685 11.441 12.6685 11.1465C12.6685 10.8519 12.4298 10.6132 12.1352 10.6132H7.86854Z"),s(n,"fill","currentColor"),s(i,"fill-rule","evenodd"),s(i,"clip-rule","evenodd"),s(i,"d","M2.13331 1.06665C1.5442 1.06665 1.06664 1.54421 1.06664 2.13332V13.8667C1.06664 14.4558 1.5442 14.9333 2.13331 14.9333H13.8667C14.4558 14.9333 14.9333 14.4558 14.9333 13.8667V2.13332C14.9333 1.54421 14.4558 1.06665 13.8667 1.06665H2.13331ZM2.13331 2.13332H13.8667V13.8667H2.13331V2.13332Z"),s(i,"fill","currentColor"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"fill","none"),s(t,"xmlns","http://www.w3.org/2000/svg")},m(o,h){H(o,t,h),u(t,e),u(t,r),u(t,n),u(t,i)},p:c,i:c,o:c,d(o){o&&S(t)}}}class rt extends C{constructor(t){super(),x(this,t,null,T,b,{})}}function X(a){let t,e,r,n,i,o,h,v=[{class:"status-indicator-container"},{role:"status"},{"aria-label":o="Agent status: "+a[1]},{title:h="Status: "+a[1]},I(a[2])],l={};for(let d=0;d<v.length;d+=1)l=m(l,v[d]);return{c(){t=y("div"),e=y("div"),r=O(),n=y("div"),i=j(a[1]),s(e,"class","status-dot svelte-v4itgx"),s(n,"class","status-label svelte-v4itgx"),$(t,l),w(t,"expanded",a[0]),w(t,"collapsed",!a[0]),w(t,"svelte-v4itgx",!0)},m(d,g){H(d,t,g),u(t,e),u(t,r),u(t,n),u(n,i)},p(d,[g]){2&g&&U(i,d[1]),$(t,l=V(v,[{class:"status-indicator-container"},{role:"status"},2&g&&o!==(o="Agent status: "+d[1])&&{"aria-label":o},2&g&&h!==(h="Status: "+d[1])&&{title:h},4&g&&I(d[2])])),w(t,"expanded",d[0]),w(t,"collapsed",!d[0]),w(t,"svelte-v4itgx",!0)},i:c,o:c,d(d){d&&S(t)}}}function Y(a,t,e){let r,n,{status:i}=t,{workspaceStatus:o}=t,{isExpanded:h=!1}=t,{hasUpdates:v=!1}=t;return a.$$set=l=>{"status"in l&&e(3,i=l.status),"workspaceStatus"in l&&e(4,o=l.workspaceStatus),"isExpanded"in l&&e(0,h=l.isExpanded),"hasUpdates"in l&&e(5,v=l.hasUpdates)},a.$$.update=()=>{56&a.$$.dirty&&e(1,r=z(i,o,v)),2&a.$$.dirty&&e(2,n=J(r))},[h,r,n,i,o,v]}class nt extends C{constructor(t){super(),x(this,t,Y,X,b,{status:3,workspaceStatus:4,isExpanded:0,hasUpdates:5})}}export{tt as S,rt as T,nt as a,it as b,at as c,st as d,Q as e,et as v};

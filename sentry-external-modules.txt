com.github.ben-manes.caffeine:caffeine:3.1.8
com.google.android:annotations:4.1.1.4
com.google.api.grpc:proto-google-common-protos:2.17.0
com.google.auth:google-auth-library-credentials:1.4.0
com.google.auth:google-auth-library-oauth2-http:1.4.0
com.google.auto.value:auto-value-annotations:1.10.1
com.google.code.findbugs:jsr305:3.0.2
com.google.code.gson:gson:2.10.1
com.google.errorprone:error_prone_annotations:2.21.1
com.google.guava:failureaccess:1.0.1
com.google.guava:guava:32.0.1-jre
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
com.google.http-client:google-http-client-gson:1.41.0
com.google.http-client:google-http-client:1.41.0
com.google.j2objc:j2objc-annotations:2.8
com.google.protobuf:protobuf-java-util:3.24.4
com.google.protobuf:protobuf-java:3.24.4
com.google.protobuf:protobuf-kotlin:3.24.4
com.google.re2j:re2j:1.7
com.squareup.okio:okio:1.17.5
commons-codec:commons-codec:1.11
commons-logging:commons-logging:1.2
io.grpc:grpc-all:1.57.2
io.grpc:grpc-alts:1.57.2
io.grpc:grpc-api:1.57.2
io.grpc:grpc-auth:1.57.2
io.grpc:grpc-context:1.57.2
io.grpc:grpc-core:1.57.2
io.grpc:grpc-grpclb:1.57.2
io.grpc:grpc-kotlin-stub:1.4.0
io.grpc:grpc-netty-shaded:1.57.2
io.grpc:grpc-netty:1.57.2
io.grpc:grpc-okhttp:1.57.2
io.grpc:grpc-protobuf-lite:1.57.2
io.grpc:grpc-protobuf:1.57.2
io.grpc:grpc-rls:1.57.2
io.grpc:grpc-services:1.57.2
io.grpc:grpc-servlet-jakarta:1.57.2
io.grpc:grpc-servlet:1.57.2
io.grpc:grpc-stub:1.57.2
io.grpc:grpc-testing:1.57.2
io.grpc:grpc-xds:1.57.2
io.ktor:ktor-client-cio-jvm:2.3.12
io.ktor:ktor-client-core-jvm:2.3.12
io.ktor:ktor-events-jvm:2.3.12
io.ktor:ktor-http-cio-jvm:2.3.12
io.ktor:ktor-http-jvm:2.3.12
io.ktor:ktor-io-jvm:2.3.12
io.ktor:ktor-network-jvm:2.3.12
io.ktor:ktor-network-tls-jvm:2.3.12
io.ktor:ktor-serialization-jvm:2.3.12
io.ktor:ktor-utils-jvm:2.3.12
io.ktor:ktor-websocket-serialization-jvm:2.3.12
io.ktor:ktor-websockets-jvm:2.3.12
io.netty:netty-buffer:4.1.93.Final
io.netty:netty-codec-http2:4.1.93.Final
io.netty:netty-codec-http:4.1.93.Final
io.netty:netty-codec-socks:4.1.93.Final
io.netty:netty-codec:4.1.93.Final
io.netty:netty-common:4.1.93.Final
io.netty:netty-handler-proxy:4.1.93.Final
io.netty:netty-handler:4.1.93.Final
io.netty:netty-resolver:4.1.93.Final
io.netty:netty-transport-native-unix-common:4.1.93.Final
io.netty:netty-transport:4.1.93.Final
io.opencensus:opencensus-api:0.28.0
io.opencensus:opencensus-contrib-http-util:0.28.0
io.opencensus:opencensus-proto:0.2.0
io.perfmark:perfmark-api:0.26.0
io.sentry:sentry-kotlin-extensions:8.12.0
io.sentry:sentry:8.12.0
javax.annotation:javax.annotation-api:1.3.2
junit:junit:4.13.2
org.apache.httpcomponents:httpclient:4.5.13
org.apache.httpcomponents:httpcore:4.4.15
org.checkerframework:checker-qual:3.37.0
org.codehaus.mojo:animal-sniffer-annotations:1.23
org.conscrypt:conscrypt-openjdk-uber:2.5.2
org.hamcrest:hamcrest-core:1.3
org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22
org.jetbrains.kotlin:kotlin-stdlib:1.8.22
org.jetbrains:annotations:13.0
org.slf4j:slf4j-api:1.7.36
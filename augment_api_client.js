#!/usr/bin/env node
/**
 * Augment AI 直接API调用客户端
 * 绕过UI界面直接与Augment AI交互
 */

const axios = require('axios').default;
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class AugmentAPIClient {
    constructor(baseUrl = null) {
        this.baseUrl = baseUrl;
        this.client = null;
        this.init();
    }

    async init() {
        if (!this.baseUrl) {
            this.baseUrl = await this.discoverPort();
        }
        
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'AugmentAPIClient/1.0'
            }
        });

        console.log(`🚀 初始化Augment API客户端: ${this.baseUrl}`);
    }

    async discoverPort() {
        console.log('🔍 正在查找IntelliJ IDEA HTTP API端口...');
        
        const commonPorts = [63342, 63343, 63344, 63345];
        
        try {
            // 尝试使用netstat查找端口
            const { stdout } = await execAsync('netstat -an');
            
            for (const port of commonPorts) {
                if (stdout.includes(`:${port}`) && stdout.includes('LISTEN')) {
                    console.log(`✅ 发现端口: ${port}`);
                    return `http://localhost:${port}`;
                }
            }
        } catch (error) {
            console.log(`⚠️ 端口发现失败: ${error.message}`);
        }
        
        const defaultPort = 63342;
        console.log(`🔧 使用默认端口: ${defaultPort}`);
        return `http://localhost:${defaultPort}`;
    }

    async testConnection() {
        console.log('🔗 测试API连接...');
        
        const testEndpoints = ['/api/about', '/api', '/'];
        
        for (const endpoint of testEndpoints) {
            try {
                const response = await this.client.get(endpoint, { timeout: 5000 });
                if (response.status === 200) {
                    console.log('✅ API连接成功');
                    return true;
                }
            } catch (error) {
                // 继续尝试下一个端点
            }
        }
        
        console.log('❌ API连接失败');
        return false;
    }

    async sendMessageDirect(text, options = {}) {
        console.log(`📤 发送消息: ${text.substring(0, 50)}...`);
        
        const payload = {
            text: text,
            chatHistory: options.chatHistory || [],
            modelId: options.modelId || 'default',
            disableRetrieval: options.disableRetrieval || false,
            silent: options.silent || false
        };

        const endpoints = [
            '/api/augment/chat/direct',
            '/api/augment/chat',
            '/api/chat/message',
            '/api/chat'
        ];

        for (const endpoint of endpoints) {
            try {
                console.log(`🎯 尝试端点: ${endpoint}`);
                const response = await this.client.post(endpoint, payload);
                
                if (response.status === 200) {
                    console.log('✅ 消息发送成功');
                    return response.data;
                }
            } catch (error) {
                console.log(`❌ 端点失败: ${error.message}`);
                continue;
            }
        }
        
        return null;
    }

    async sendMessageAsync(text) {
        console.log(`📨 异步发送消息: ${text.substring(0, 50)}...`);
        
        const requestId = Date.now().toString();
        const payload = {
            requestId: requestId,
            baseMsg: {
                '@type': 'type.googleapis.com/ChatUserMessageRequest',
                data: {
                    text: text,
                    chatHistory: [],
                    modelId: 'default'
                }
            }
        };

        const endpoints = [
            '/api/messaging/async',
            '/api/webview/message',
            '/api/augment/async'
        ];

        for (const endpoint of endpoints) {
            try {
                console.log(`🎯 尝试异步端点: ${endpoint}`);
                const response = await this.client.post(endpoint, payload);
                
                if (response.status === 200) {
                    console.log('✅ 异步消息发送成功');
                    return response.data;
                }
            } catch (error) {
                console.log(`❌ 异步端点失败: ${error.message}`);
                continue;
            }
        }
        
        return null;
    }

    async sendMessageWebView(text) {
        console.log(`🌐 WebView发送消息: ${text.substring(0, 50)}...`);
        
        const payload = {
            type: 'chat-user-message',
            data: {
                text: text,
                chatHistory: [],
                modelId: 'default',
                disableRetrieval: false
            }
        };

        const endpoints = [
            '/api/webview/chat',
            '/api/webview/message',
            '/api/ide/webview'
        ];

        for (const endpoint of endpoints) {
            try {
                console.log(`🎯 尝试WebView端点: ${endpoint}`);
                const response = await this.client.post(endpoint, payload);
                
                if (response.status === 200) {
                    console.log('✅ WebView消息发送成功');
                    return response.data;
                }
            } catch (error) {
                console.log(`❌ WebView端点失败: ${error.message}`);
                continue;
            }
        }
        
        return null;
    }

    async sendMessageAllMethods(text) {
        console.log(`🚀 尝试所有方法发送消息: ${text}`);
        console.log('='.repeat(60));
        
        // 方法1：直接API
        let result = await this.sendMessageDirect(text);
        if (result) {
            return result;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 方法2：异步API
        result = await this.sendMessageAsync(text);
        if (result) {
            return result;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 方法3：WebView API
        result = await this.sendMessageWebView(text);
        if (result) {
            return result;
        }
        
        console.log('\n❌ 所有方法都失败了');
        return null;
    }
}

async function main() {
    console.log('🤖 Augment AI 直接API调用工具');
    console.log('='.repeat(50));
    
    // 获取命令行参数
    const args = process.argv.slice(2);
    if (args.length === 0) {
        console.log('用法: node augment_api_client.js "你的消息内容"');
        console.log('示例: node augment_api_client.js "解释这个项目的结构"');
        process.exit(1);
    }
    
    const message = args.join(' ');
    
    try {
        // 初始化API客户端
        const api = new AugmentAPIClient();
        
        // 测试连接
        if (!(await api.testConnection())) {
            console.log('❌ 无法连接到Augment API，请确保：');
            console.log('   1. IntelliJ IDEA正在运行');
            console.log('   2. Augment插件已安装并启用');
            console.log('   3. 防火墙允许本地连接');
            process.exit(1);
        }
        
        console.log('\n' + '='.repeat(50));
        
        // 发送消息
        const result = await api.sendMessageAllMethods(message);
        
        if (result) {
            console.log('\n✅ 成功收到回复:');
            console.log('📝', JSON.stringify(result, null, 2));
        } else {
            console.log('\n❌ 消息发送失败');
            console.log('\n🔧 调试建议：');
            console.log('  1. 检查IntelliJ IDEA日志');
            console.log('  2. 确认Augment插件版本');
            console.log('  3. 尝试重启IDE');
            console.log('  4. 检查网络防火墙设置');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ 发生错误:', error.message);
        process.exit(1);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 完成！');
}

// 如果直接运行脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = AugmentAPIClient;

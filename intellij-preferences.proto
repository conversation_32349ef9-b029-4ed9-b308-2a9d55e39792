syntax = "proto3";

package com.augmentcode.rpc;

// This package contains proto version of preferences WebView messages defined in webview-messages.ts.

// Add these options to generate Java classes
option java_multiple_files = true;
option java_package = "com.augmentcode.rpc";
option java_outer_classname = "PreferencesTypes";

import "google/protobuf/empty.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/struct.proto";

import "augment.proto";
import "intellij-chat.proto";

/*
 * This service defines an abstraction of an interface that the preferences web view can use.
 * Web views send messages via JS objects which we parse as JSONs into the proto buffers.
 * Until web views are not using HTTP as transport protocol, routing of the JS messages
 * into the service calls below is managed by AugmentMessagingServiceImpl.
 */
service WebviewPreferencesService {
  rpc PreferenceNotify(PreferenceNotifyRequest) returns (google.protobuf.Empty);
  rpc PreferencesLoaded(PreferencesLoadedRequest) returns (PreferencesInitializeResponse) {}
  rpc PreferenceResult(PreferenceResultRequest) returns (google.protobuf.Empty) {}
}

message PreferenceNotifyRequest {
  string message = 1;
}

message PreferencesLoadedRequest {
  option (webview_message_type) = "preference-panel-loaded";
}

message PreferencesInitializeResponse {
  option (webview_message_type) = "preference-init";
  PreferenceInput data = 1;
}

message PreferenceInput {
  string type = 1; // e.g., "Chat"
  PreferencePair data = 2;
  bool enable_retrieval_data_collection = 3;
}

message PreferencePair {
  AugmentChatEntry a = 1;
  AugmentChatEntry b = 2;
}

message AugmentChatEntry {
  string message = 1;
  string response = 2;
  // TODO: Add occuredAt
}

message PreferenceResultRequest {
  option (webview_message_type) = "preference-result-message";
  PreferenceResult data = 1;
}

message PreferenceResult {
  string overall_rating = 1;
  string formatting_rating = 2;
  string hallucination_rating = 3;
  string instruction_following_rating = 4;
  string text_feedback = 5;
  bool is_high_quality = 6;
}

message ChatStreamDoneMessage {
  option (webview_message_type) = "chat-stream-done";
}

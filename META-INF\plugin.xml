<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
  <idea-version since-build="243" />
  <change-notes><![CDATA[<h1>Augment IntelliJ Plugin Release Notes</h1>

<h2>Version 0.258.2</h2>

<h2>[*o*] Major Features</h2>
<ul><li><strong>Task List Integration</strong>: You can now access and manage your development tasks directly within the JetBrains environment, streamlining your workflow by keeping track of to-dos and project items without leaving your IDE</li></ul>

<h2>[-_-] Improvements &amp; Fixes</h2>
<p>-<strong>Freezes in Windows</strong>: In 0.258.1 we noticed that plugin would result in spawning lots of node processes and freezing eventually, this patch release fixes it</p>
<ul><li><strong>Smoother File Operations</strong>: File creation now happens seamlessly in the background, preventing version control dialogs from interrupting your chat flow</li><li><strong>Polished Chat Experience</strong>: Enjoy cleaner message layouts, better visual spacing, and more intuitive navigation throughout your conversations</li><li><strong>Enhanced Performance</strong>: Better memory usage, improved sidecar launching, and more reliable background processes contribute to a smoother plugin experience</li><li><strong>Chat Interface Enhancements</strong>: Fixed chat message edit box width issues and improved text wrapping on feedback panels for better readability</li><li><strong>Image Handling</strong>: You can now preview images in a modal window, and images are automatically inserted at the end of chat content for consistent formatting</li><li><strong>Keyboard Shortcuts</strong>: Improved keybinding infrastructure with the Cmd+. shortcut now cleanly toggling between auto and manual modes instead of cycling through all modes</li><li><strong>Tool Integration</strong>: Fixed missing play buttons for fetch and MCP tools, and improved tool component click behavior and hover styles</li></ul>]]></change-notes>
  <description><![CDATA[<p><strong>Augment Code</strong> is the most advanced AI coding assistant for JetBrains IDEs, designed for professional software engineers working with large, complex codebases. Our AI Agent, intelligent code completions, and context-aware chat accelerate your development workflow while maintaining code quality and consistency.</p>

<p><a href="https://www.augmentcode.com"><img src="https://augment-assets.com/augment-hero-sm.png" alt="Augment Code" /></a></p>

<h2>Why Augment?</h2>

<p>Augment is not just another AI coding tool – it's an <strong>AI-powered coding platform</strong> built specifically for enterprise-scale development:</p>

<ul><li><strong>Advanced AI Agent</strong>: Complete complex tasks, build features, and solve production issues with an AI agent that deeply understands your codebase</li><li><strong>Lightning-fast AI Code Completions</strong>: Get intelligent, context-aware suggestions as you type, tailored to your project's patterns and conventions</li><li><strong>Intelligent AI Chat</strong>: Ask questions, plan features, and get instant answers with deep codebase understanding</li><li><strong>World-class Context Engine</strong>: Our proprietary context engine understands your entire codebase, dependencies, and project structure</li><li><strong>Native Integrations</strong>: Connect with Jira, Linear, Notion, GitHub, and more to bring all your development context together</li></ul>

<h3>Get up to speed instantly</h3>

<p>Whether you're exploring a new codebase or diving into unfamiliar territory, Augment's AI helps you understand complex systems in minutes. Our AI coding assistant can:</p>

<ul><li>Explain how systems work</li><li>Help investigate bugs</li><li>Guide you through new APIs and frameworks</li><li>Accelerate onboarding to new projects</li></ul>

<h3>Make updates with confidence</h3>

<p>In production-grade software, there are no simple changes. Augment's AI Agent manages the complexity for you by understanding:</p>

<ul><li>Your code structure and patterns</li><li>API contracts and schemas</li><li>Dependencies and their interactions</li><li>Best practices specific to your codebase</li></ul>

<h2>Key Features</h2>

<h3>AI Agent - Your Intelligent Coding Partner</h3>

<p>Let our AI Agent handle complex engineering tasks while you focus on architecture and design. The Agent can:</p>

<ul><li><strong>Complete Features</strong>: Build entire features with proper error handling and tests</li><li><strong>Fix Bugs</strong>: Investigate and resolve production issues with full context</li><li><strong>Refactor Code</strong>: Modernize legacy code while maintaining functionality</li><li><strong>Write Tests</strong>: Generate comprehensive test suites that actually work</li></ul>

<h3>Intelligent Chat with Smart Apply</h3>

<p>Get instant answers and apply changes with confidence:</p>

<ul><li><strong>Codebase Q&amp;A</strong>: Ask anything about your code and get accurate answers</li><li><strong>Smart Apply</strong>: One-click code updates that respect your project's patterns</li><li><strong>Multi-file Edits</strong>: Make coordinated changes across multiple files</li><li><strong>Context Awareness</strong>: Chat understands your current file, recent changes, and project structure</li></ul>

<h3>Lightning-Fast Code Completions</h3>

<p>Experience AI-powered completions that feel like magic:</p>

<ul><li><strong>Instant Suggestions</strong>: Sub-100ms latency for seamless coding flow</li><li><strong>Multi-line Completions</strong>: Complete entire functions and code blocks</li><li><strong>Pattern Recognition</strong>: Learns from your codebase to suggest idiomatic code</li><li><strong>Language Support</strong>: Works with all major programming languages</li></ul>

<h2>Supported IDEs</h2>

<p>Augment works seamlessly with all JetBrains IDEs:</p>

<ul><li>IntelliJ IDEA (Community &amp; Ultimate)</li><li>PyCharm (Community &amp; Professional)</li><li>WebStorm</li><li>GoLand</li><li>PhpStorm</li><li>CLion</li><li>RubyMine</li><li>DataGrip</li><li>Rider</li><li>Android Studio</li><li>And more!</li></ul>

<h2>Installation</h2>

<ol><li>Open your JetBrains IDE</li><li>Go to <strong>Settings/Preferences</strong> → <strong>Plugins</strong></li><li>Search for <strong>&quot;Augment&quot;</strong> in the Marketplace</li><li>Click <strong>Install</strong> and restart your IDE</li><li>Sign in with your Augment account to start coding with AI</li></ol>

<h2>Getting Started</h2>

<h3>Quick Start</h3>

<ol><li><strong>Install the Plugin</strong>: Follow the installation steps above</li><li><strong>Sign In</strong>: Click &quot;Start using Augment&quot; to sign up for a 7-day free trial</li><li><strong>Open Chat</strong>: Press <code>Ctrl+Alt+I</code> (Windows/Linux) or <code>Cmd+Ctrl+I</code> (macOS)</li><li><strong>Enable Completions</strong>: Code completions are enabled by default</li><li><strong>Try Agent</strong>: Ask the AI to complete a task or build a feature</li></ol>

<h3>Essential Shortcuts</h3>

<ul><li><strong>Open Chat</strong>: <code>Ctrl+Alt+I</code> / <code>Cmd+Ctrl+I</code></li><li><strong>Show History</strong>: <code>Ctrl+Alt+7</code> / <code>Cmd+Ctrl+7</code></li><li><strong>Toggle Completions</strong>: <code>Ctrl+Alt+9</code> / <code>Cmd+Ctrl+9</code></li></ul>

<h2>Configuration</h2>

<h3>Customize Your Experience</h3>

<ul><li><strong>Completions</strong>: Configure when and how completions appear</li><li><strong>File Types</strong>: Disable Augment for specific file types if needed</li><li><strong>Integrations</strong>: Connect your development tools for enhanced context</li></ul>

<p>Access settings via <strong>Settings/Preferences</strong> → <strong>Editor</strong> → <strong>Augment</strong></p>

<h2>Pro Tips</h2>

<ol><li><strong>Use Natural Language</strong>: Chat understands context, so ask questions naturally</li><li><strong>Leverage Integrations</strong>: Connect Jira, Linear, or Notion for richer context</li><li><strong>Review Agent Changes</strong>: Always review AI-generated code before committing</li><li><strong>Provide Feedback</strong>: Use thumbs up/down to help improve suggestions</li></ol>

<h2>Resources</h2>

<ul><li><a href="https://docs.augmentcode.com/">Documentation</a></li><li><a href="https://discord.gg/augmentcode">Community Discord</a></li><li><a href="https://support.augmentcode.com/">Support &amp; Issues</a></li><li><a href="https://www.augmentcode.com/contact">Enterprise Solutions</a></li></ul>

<h2>Pricing</h2>

<p>Get started with a <strong>7-day free trial</strong> including Agent, Chat, and Completions. After your trial:</p>

<ul><li><strong>Professional</strong>: Best for individual developers</li><li><strong>Team</strong>: Collaborate with your entire team</li><li><strong>Enterprise</strong>: Custom solutions for large organizations</li></ul>

<p>Visit <a href="https://augmentcode.com/pricing">augmentcode.com/pricing</a> for details.</p>]]></description>
  <version>0.258.2</version>
  <id>com.augmentcode</id>
  <name>Augment: AI coding assistant for professionals</name>
  <vendor>Augment Computing</vendor>
  <dependencies>
    <plugin id="com.intellij.modules.platform" />
    <plugin id="com.jetbrains.sh" />
    <plugin id="org.jetbrains.plugins.terminal" />
    <plugin id="com.intellij.modules.lang" />
    <!-- to make incompatible with Gateway Client -->
    <module name="intellij.platform.collaborationTools" />
    <!-- for OAuth support -->
  </dependencies>
  <resource-bundle>messages.AugmentBundle</resource-bundle>
  <extensions defaultExtensionNs="com.intellij">
    <notificationGroup id="augment.plugin.updates" displayType="STICKY_BALLOON" key="augment.notification.group.name" bundle="messages.AugmentBundle" />
    <applicationService serviceImplementation="com.augmentcode.intellij.idea.AugmentPluginUpdater" />
    <toolWindow id="Augment" anchor="right" icon="AugmentIcons.StatusInitial" factoryClass="com.augmentcode.intellij.chat.AugmentChatToolWindowFactory" />
    <postStartupActivity implementation="com.augmentcode.intellij.sentry.SentryStartupActivity" />
    <postStartupActivity implementation="com.augmentcode.intellij.index.AugmentValidateIndexActivity" />
    <postStartupActivity implementation="com.augmentcode.intellij.chat.AugmentChatProjectActivity" />
    <postStartupActivity implementation="com.augmentcode.intellij.idea.AugmentPostStartupActivity" />
    <errorHandler implementation="com.augmentcode.intellij.sentry.SentryErrorReportSubmitter" />
    <applicationService serviceImplementation="com.augmentcode.intellij.settings.AugmentSettings" />
    <applicationService serviceImplementation="com.augmentcode.intellij.settings.AugmentIntegrationsConfig" />
    <applicationService serviceInterface="com.augmentcode.intellij.api.AugmentAPI" serviceImplementation="com.augmentcode.intellij.api.AugmentAPIImpl" />
    <applicationService serviceInterface="com.augmentcode.intellij.api.HttpClientProvider" serviceImplementation="com.augmentcode.intellij.api.HttpClientProviderImpl" />
    <applicationConfigurable parentId="editor" id="com.augmentcode.intellij.settings" instance="com.augmentcode.intellij.settings.AugmentSettingsConfigurable" displayName="Augment" />
    <statusBarWidgetFactory implementation="com.augmentcode.intellij.status.AugmentStatusBarWidgetFactory" id="AugmentStatusBarWidget" />
    <projectService serviceInterface="com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager" serviceImplementation="com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl" />
    <projectService serviceInterface="com.augmentcode.intellij.webviews.AugmentMessagingService" serviceImplementation="com.augmentcode.intellij.webviews.chat.ChatMessagingService" />
    <projectService serviceImplementation="com.augmentcode.intellij.metrics.OnboardingSessionEventReporter" />
    <projectService serviceImplementation="com.augmentcode.intellij.index.AugmentEditorHistoryService" />
    <fileBasedIndex implementation="com.augmentcode.intellij.index.AugmentLocalIndex" />
    <fileBasedIndex implementation="com.augmentcode.intellij.guidelines.AugmentWorkspaceGuidelinesIndex" />
    <fileBasedIndex implementation="com.augmentcode.intellij.workspacemanagement.indexing.WorkspaceIndex" />
    <inline.completion.provider order="first" implementation="com.augmentcode.intellij.completion.AugmentCompletionProvider" />
    <inline.completion.element.manipulator implementation="com.augmentcode.intellij.completion.AugmentCompletionElementManipulator" order="first" />
    <httpRequestHandler implementation="com.augmentcode.intellij.auth.AugmentOAuthCallbackHandler" />
    <notificationGroup id="augment.notifications" displayType="BALLOON" isLogByDefault="false" />
    <editorFactoryListener implementation="com.augmentcode.intellij.index.AugmentEditorFocusListener" />
    <editorFactoryListener implementation="com.augmentcode.intellij.chat.AugmentChatSelectionListener" />
    <diff.DiffExtension implementation="com.augmentcode.intellij.listeners.SmartPasteDiffExtension" />
  </extensions>
  <projectListeners>
    <listener class="com.augmentcode.intellij.listeners.AugmentVFSListener" topic="com.intellij.openapi.vfs.newvfs.BulkFileListener" />
  </projectListeners>
  <applicationListeners>
    <listener class="com.augmentcode.intellij.completion.AugmentInlineCompletionListener" topic="com.intellij.codeInsight.inline.completion.InlineCompletionInstallListener" />
  </applicationListeners>
  <actions>
    <action id="com.augmentcode.intellij.actions.ShowSettingsAction" class="com.augmentcode.intellij.actions.ShowSettingsAction" text="Plugin Settings" description="Show settings for the Augment plugin" />
    <action id="com.augmentcode.intellij.actions.OpenSettingsWebviewAction" class="com.augmentcode.intellij.actions.OpenSettingsWebviewAction" icon="AllIcons.General.Settings" text="Tools Settings" description="Open Augment Tools Settings" />
    <action id="com.augmentcode.intellij.actions.ShowHelpAction" class="com.augmentcode.intellij.actions.ShowHelpAction" icon="AllIcons.Actions.Help" text="Help" description="Open Augment documentation" />
    <action id="com.augmentcode.intellij.guidelines.OpenWorkspaceGuidelinesCommand" class="com.augmentcode.intellij.guidelines.OpenWorkspaceGuidelinesCommand" text="Edit Workspace Guidelines for Augment" description="Open the workspace guidelines file for editing" />
    <action id="com.augmentcode.intellij.actions.ExtensionStatusAction" class="com.augmentcode.intellij.actions.ExtensionStatusAction" text="Show Extension Status" description="Show the status of the extension. This can be helpful for debugging." />
    <action id="com.augmentcode.intellij.actions.SignInAction" class="com.augmentcode.intellij.actions.SignInAction" text="Sign In" description="Sign in to Augment" />
    <action id="com.augmentcode.intellij.actions.SignOutAction" class="com.augmentcode.intellij.actions.SignOutAction" text="Sign Out" description="Sign out of Augment" />
    <action id="com.augmentcode.intellij.actions.ShowChatAction" class="com.augmentcode.intellij.actions.ShowChatAction" text="Show Chat" description="Show the Augment Chat panel">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt I" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control I" />
    </action>
    <action id="com.augmentcode.intellij.actions.ShowHistoryAction" class="com.augmentcode.intellij.actions.ShowHistoryAction" text="Show History" description="Show the Augment History panel">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt 7" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control 7" />
    </action>
    "
    <action id="com.augmentcode.intellij.actions.ToggleCompletionsAction" class="com.augmentcode.intellij.actions.ToggleCompletionsAction" text="Toggle Completions" description="Toggle automatic inline completions on/off">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt 9" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control 9" />
    </action>
    <action id="com.augmentcode.intellij.actions.GenerateSyncReport" class="com.augmentcode.intellij.actions.GenerateSyncReport" text="Generate Sync Report" description="Generate a report of synced files and stats" />
    <action id="com.augmentcode.intellij.actions.ManageAccountAction" class="com.augmentcode.intellij.actions.ManageAccountAction" text="Manage Account" description="Open the Augment account management page in browser" />
    <action id="com.augmentcode.intellij.actions.ReindexAction" class="com.augmentcode.intellij.actions.ReindexAction" text="Rebuild Augment Index" description="Rebuild the Augment local index. This can be helpful when the index gets out of sync." />
  </actions>
</idea-plugin>

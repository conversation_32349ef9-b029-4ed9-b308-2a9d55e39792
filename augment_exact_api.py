#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment AI 精确API调用脚本
基于源代码分析的确切实现
"""

import requests
import json
import time

class AugmentExactAPI:
    def __init__(self):
        self.base_url = "http://localhost:63342"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def send_webview_message(self, message_type: str, data: dict) -> dict:
        """
        发送WebView消息 - 基于源代码的确切实现
        
        根据 sidecar/index.js 中的实现：
        connection2.onRequest("augmentcode/webview-message", async (params) => {
            const request = ProcessWebviewMessageRequest.fromJson(params, {
                ignoreUnknownFields: true
            });
            const msg = JSON.parse(request.message);
            const result = await new Promise((resolve4) => {
                getWebviewMessaging().onMessage(msg, (response) => {
                    resolve4(response);
                });
            });
        """
        
        # 构建消息格式
        message = {
            "type": message_type,
            "data": data
        }
        
        # 构建请求格式 - 基于 ProcessWebviewMessageRequest
        request_payload = {
            "message": json.dumps(message)
        }
        
        print(f"📤 发送WebView消息: {message_type}")
        print(f"📝 消息内容: {json.dumps(message, ensure_ascii=False, indent=2)}")
        
        try:
            # 发送到LSP服务器的webview-message端点
            response = self.session.post(
                f"{self.base_url}/api/lsp/request",
                json={
                    "method": "augmentcode/webview-message",
                    "params": request_payload
                },
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ WebView消息发送成功")
                return response.json()
            else:
                print(f"❌ WebView消息发送失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 发送异常: {e}")
            return None
    
    def send_chat_message(self, text: str, **kwargs) -> dict:
        """
        发送聊天消息 - 使用确切的协议格式
        
        基于 intellij-chat.proto 中的定义：
        message ChatUserMessageRequest {
            option (webview_message_type) = "chat-user-message";
            ChatUserMessageData data = 1;
        }
        """
        
        # 构建聊天数据 - 基于 ChatUserMessageData
        chat_data = {
            "text": text,
            "chatHistory": kwargs.get('chat_history', []),
            "modelId": kwargs.get('model_id', 'default'),
            "disableRetrieval": kwargs.get('disable_retrieval', False),
            "disableSelectedCodeDetails": kwargs.get('disable_selected_code_details', False),
            "silent": kwargs.get('silent', False)
        }
        
        # 发送WebView消息
        return self.send_webview_message("chat-user-message", chat_data)
    
    def send_async_message(self, text: str) -> dict:
        """
        发送异步包装消息
        
        基于 augment.proto 中的 AsyncWrapper 定义
        """
        
        request_id = str(int(time.time() * 1000))
        
        # 构建异步包装消息
        async_data = {
            "requestId": request_id,
            "baseMsg": {
                "type": "chat-user-message",
                "data": {
                    "text": text,
                    "chatHistory": [],
                    "modelId": "default",
                    "disableRetrieval": False
                }
            }
        }
        
        return self.send_webview_message("async-wrapper", async_data)
    
    def test_direct_lsp_call(self, text: str) -> dict:
        """
        直接调用LSP服务器
        """
        
        print(f"🔧 直接LSP调用: {text}")
        
        # 构建LSP请求
        lsp_request = {
            "jsonrpc": "2.0",
            "id": int(time.time() * 1000),
            "method": "augmentcode/webview-message",
            "params": {
                "message": json.dumps({
                    "type": "chat-user-message",
                    "data": {
                        "text": text,
                        "chatHistory": [],
                        "modelId": "default"
                    }
                })
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/lsp",
                json=lsp_request,
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ LSP调用成功")
                return response.json()
            else:
                print(f"❌ LSP调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ LSP调用异常: {e}")
            return None

def main():
    print("🎯 Augment AI 精确API调用工具")
    print("基于源代码分析的确切实现")
    print("=" * 60)
    
    api = AugmentExactAPI()
    
    # 测试消息
    test_message = "你好，Augment AI！请介绍一下你的功能。"
    
    print("\n🧪 测试1: WebView聊天消息")
    print("-" * 40)
    result1 = api.send_chat_message(test_message)
    if result1:
        print(f"📝 响应: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    print("\n🧪 测试2: 异步包装消息")
    print("-" * 40)
    result2 = api.send_async_message(test_message)
    if result2:
        print(f"📝 响应: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    print("\n🧪 测试3: 直接LSP调用")
    print("-" * 40)
    result3 = api.test_direct_lsp_call(test_message)
    if result3:
        print(f"📝 响应: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    print("\n" + "=" * 60)
    
    if result1 or result2 or result3:
        print("🎉 成功找到有效的API调用方式！")
    else:
        print("❌ 所有方法都失败了")
        print("\n🔧 可能的原因：")
        print("  1. Augment插件未完全加载")
        print("  2. LSP服务器未启动")
        print("  3. 需要特定的认证或会话")
        print("  4. API端点路径不正确")

if __name__ == "__main__":
    main()

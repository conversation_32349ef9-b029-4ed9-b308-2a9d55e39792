#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发Augment输入框的回车键发送消息
基于源代码分析的确切实现
"""

import time
import subprocess
import sys

def trigger_enter_in_augment():
    """触发Augment输入框的回车键"""
    
    print("🎯 触发Augment输入框的回车键发送消息")
    print("=" * 60)
    
    # 基于源代码分析：
    # keypress-DD1aQVr0.js: function r(t,o){return e=>!e.shiftKey&&e.key===t&&(o(e),!0)}
    # 这个函数检查按键是否是特定键（如Enter）且没有按Shift键
    
    print("📋 基于源代码分析的发现：")
    print("1. keypress函数检查: !e.shiftKey && e.key === 'Enter'")
    print("2. 如果条件满足，调用回调函数 o(e)")
    print("3. 返回 true 表示事件已处理")
    
    print("\n🔧 方法1: 使用JavaScript注入")
    js_code = """
    // 查找Augment的输入框
    const textareas = document.querySelectorAll('textarea');
    const inputs = document.querySelectorAll('input[type="text"]');
    const editableElements = document.querySelectorAll('[contenteditable="true"]');
    
    // 合并所有可能的输入元素
    const allInputs = [...textareas, ...inputs, ...editableElements];
    
    console.log('找到输入元素:', allInputs.length);
    
    // 查找最可能的Augment输入框
    let targetElement = null;
    
    // 优先查找包含特定类名或属性的元素
    for (const element of allInputs) {
        const className = element.className || '';
        const placeholder = element.placeholder || '';
        
        if (className.includes('augment') || 
            className.includes('chat') || 
            className.includes('input') ||
            placeholder.includes('Ask') ||
            placeholder.includes('Message')) {
            targetElement = element;
            console.log('找到目标元素:', element);
            break;
        }
    }
    
    // 如果没找到特定元素，使用第一个textarea
    if (!targetElement && textareas.length > 0) {
        targetElement = textareas[0];
        console.log('使用第一个textarea:', targetElement);
    }
    
    if (targetElement) {
        // 确保元素获得焦点
        targetElement.focus();
        
        // 创建Enter键事件
        const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            shiftKey: false,
            ctrlKey: false,
            altKey: false,
            metaKey: false,
            bubbles: true,
            cancelable: true
        });
        
        console.log('发送Enter键事件...');
        
        // 分派事件
        const result = targetElement.dispatchEvent(enterEvent);
        
        console.log('Enter键事件已发送，结果:', result);
        
        // 也尝试keypress和keyup事件
        const keypressEvent = new KeyboardEvent('keypress', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            shiftKey: false,
            bubbles: true,
            cancelable: true
        });
        
        const keyupEvent = new KeyboardEvent('keyup', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            shiftKey: false,
            bubbles: true,
            cancelable: true
        });
        
        targetElement.dispatchEvent(keypressEvent);
        targetElement.dispatchEvent(keyupEvent);
        
        // 尝试触发表单提交
        const form = targetElement.closest('form');
        if (form) {
            console.log('找到表单，尝试提交...');
            form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        }
        
        // 查找并点击发送按钮
        const sendButtons = document.querySelectorAll('button');
        for (const button of sendButtons) {
            const text = button.textContent || button.innerText || '';
            const title = button.title || '';
            const ariaLabel = button.getAttribute('aria-label') || '';
            
            if (text.includes('Send') || 
                text.includes('发送') || 
                title.includes('Send') ||
                ariaLabel.includes('Send') ||
                button.type === 'submit') {
                console.log('找到发送按钮，点击...', button);
                button.click();
                break;
            }
        }
        
        return true;
    } else {
        console.log('未找到输入元素');
        return false;
    }
    """
    
    try:
        # 使用Chrome DevTools Protocol执行JavaScript
        print("📤 执行JavaScript代码...")
        
        # 方法1: 尝试通过Chrome DevTools
        chrome_cmd = [
            "chrome", "--remote-debugging-port=9222", "--disable-web-security",
            "--user-data-dir=/tmp/chrome-debug"
        ]
        
        # 启动Chrome（如果还没运行）
        try:
            subprocess.run(["pgrep", "chrome"], check=True, capture_output=True)
            print("✅ Chrome已在运行")
        except subprocess.CalledProcessError:
            print("🚀 启动Chrome...")
            subprocess.Popen(chrome_cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            time.sleep(3)
        
        # 使用curl调用Chrome DevTools API
        import json
        import requests
        
        # 获取标签页列表
        tabs_response = requests.get("http://localhost:9222/json")
        tabs = tabs_response.json()
        
        # 查找Augment相关的标签页
        augment_tab = None
        for tab in tabs:
            if "augment" in tab.get("url", "").lower() or "localhost:63342" in tab.get("url", ""):
                augment_tab = tab
                break
        
        if augment_tab:
            print(f"✅ 找到Augment标签页: {augment_tab['title']}")
            
            # 执行JavaScript
            ws_url = augment_tab["webSocketDebuggerUrl"]
            
            # 使用requests发送命令（简化版）
            runtime_evaluate = {
                "id": 1,
                "method": "Runtime.evaluate",
                "params": {
                    "expression": js_code,
                    "returnByValue": True
                }
            }
            
            print("📤 通过DevTools执行JavaScript...")
            # 这里需要WebSocket连接，简化处理
            print("⚠️ 需要WebSocket连接到Chrome DevTools")
            
        else:
            print("❌ 未找到Augment标签页")
            
    except Exception as e:
        print(f"❌ Chrome DevTools方法失败: {e}")
    
    print("\n🔧 方法2: 使用Selenium WebDriver")
    try:
        from selenium import webdriver
        from selenium.webdriver.common.keys import Keys
        from selenium.webdriver.common.by import By
        from selenium.webdriver.chrome.options import Options
        
        # 连接到现有的Chrome实例
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "localhost:9222")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 查找Augment标签页
        for handle in driver.window_handles:
            driver.switch_to.window(handle)
            if "augment" in driver.current_url.lower() or "localhost:63342" in driver.current_url:
                print(f"✅ 切换到Augment标签页: {driver.title}")
                break
        
        # 查找输入框
        input_selectors = [
            "textarea",
            "input[type='text']",
            "[contenteditable='true']",
            ".chat-input",
            ".message-input",
            "[placeholder*='Ask']",
            "[placeholder*='Message']"
        ]
        
        target_element = None
        for selector in input_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    target_element = elements[0]
                    print(f"✅ 找到输入元素: {selector}")
                    break
            except:
                continue
        
        if target_element:
            # 确保元素可见并获得焦点
            driver.execute_script("arguments[0].scrollIntoView(true);", target_element)
            target_element.click()
            
            # 发送Enter键
            print("📤 发送Enter键...")
            target_element.send_keys(Keys.ENTER)
            
            print("✅ Enter键已发送！")
            return True
        else:
            print("❌ 未找到输入元素")
            
    except ImportError:
        print("❌ 需要安装selenium: pip install selenium")
    except Exception as e:
        print(f"❌ Selenium方法失败: {e}")
    
    print("\n🔧 方法3: 使用pyautogui模拟按键")
    try:
        import pyautogui
        
        print("⚠️ 将在3秒后发送Enter键，请确保Augment输入框已获得焦点")
        time.sleep(3)
        
        # 发送Enter键
        pyautogui.press('enter')
        
        print("✅ Enter键已通过pyautogui发送！")
        return True
        
    except ImportError:
        print("❌ 需要安装pyautogui: pip install pyautogui")
    except Exception as e:
        print(f"❌ pyautogui方法失败: {e}")
    
    return False

def main():
    print("🎯 Augment回车键触发工具")
    print("基于源代码分析的确切实现")
    print("=" * 60)
    
    success = trigger_enter_in_augment()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 回车键触发成功！")
        print("请检查Augment是否发送了消息。")
    else:
        print("❌ 回车键触发失败")
        print("\n💡 手动操作建议：")
        print("1. 确保Augment聊天界面已打开")
        print("2. 在输入框中输入消息")
        print("3. 按Enter键发送")
        print("4. 或点击发送按钮")

if __name__ == "__main__":
    main()

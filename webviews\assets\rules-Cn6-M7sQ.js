import{S as O,i as W,s as j,a3 as B,a4 as Q,D as v,t as d,q as m,a5 as U,E as h,G as w,d as y,o as _,p as q,c as x,N as K,W as D,e as R,F as b,V as T,h as F,Y as S,n as N,R as P,al as V,A as Y,ah as H}from"./SpinnerAugment-BejxPZX4.js";import"./design-system-init-Cz4_d9qQ.js";import{h as M,W as L}from"./IconButtonAugment-DmfIVAYG.js";import{B as J}from"./ButtonAugment-CVatBELe.js";import{O as X}from"./OpenFileButton-DltEK_xL.js";import{C as Z,E as tt,T as et,a as k}from"./index-LNVO_dOZ.js";import{M as I,R as A}from"./message-broker-B_Xbv83k.js";import{M as nt,R as st}from"./rules-model-CvhsIRLH.js";import{R as ot}from"./RulesModeSelector-DJARyuBd.js";import{C as rt}from"./chevron-left-rWtIkHKP.js";import{T as at}from"./CardAugment-dQY7gB6x.js";import{l as ct}from"./lodash-Dxj8rwt5.js";import"./chat-context-CmoAn9pE.js";import"./index-B528snJk.js";import"./index-BlaqVU85.js";import"./remote-agents-client-DwCOg82s.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-DHtiwZCr.js";import"./TextAreaAugment-DCLSWWW0.js";import"./BaseTextInput-B8Zm7GJJ.js";import"./async-messaging-D7f6isRQ.js";import"./file-paths-B4wu8Zer.js";import"./chevron-down-D78W1-g3.js";function G(o){let e,n,t,s,r,c,$;function l(a){o[11](a)}t=new D({props:{size:1,class:"c-field-label",$$slots:{default:[lt]},$$scope:{ctx:o}}});let f={placeholder:"When should this rules file be fetched by the Agent?",size:1};return o[1]!==void 0&&(f.value=o[1]),r=new et({props:f}),B.push(()=>Q(r,"value",l)),r.$on("input",o[12]),{c(){e=b("div"),n=b("div"),w(t.$$.fragment),s=T(),w(r.$$.fragment),F(n,"class","c-rule-field c-rule-field-full-width svelte-1r8al3d"),F(e,"class","c-rule-config svelte-1r8al3d")},m(a,p){x(a,e,p),R(e,n),h(t,n,null),R(n,s),h(r,n,null),$=!0},p(a,p){const i={};262144&p&&(i.$$scope={dirty:p,ctx:a}),t.$set(i);const u={};!c&&2&p&&(c=!0,u.value=a[1],U(()=>c=!1)),r.$set(u)},i(a){$||(m(t.$$.fragment,a),m(r.$$.fragment,a),$=!0)},o(a){d(t.$$.fragment,a),d(r.$$.fragment,a),$=!1},d(a){a&&y(e),v(t),v(r)}}}function lt(o){let e;return{c(){e=S("Description")},m(n,t){x(n,e,t)},d(n){n&&y(e)}}}function it(o){let e,n,t=o[3]===A.AGENT_REQUESTED&&G(o);return{c(){t&&t.c(),e=K()},m(s,r){t&&t.m(s,r),x(s,e,r),n=!0},p(s,r){s[3]===A.AGENT_REQUESTED?t?(t.p(s,r),8&r&&m(t,1)):(t=G(s),t.c(),m(t,1),t.m(e.parentNode,e)):t&&(_(),d(t,1,1,()=>{t=null}),q())},i(s){n||(m(t),n=!0)},o(s){d(t),n=!1},d(s){s&&y(e),t&&t.d(s)}}}function $t(o){let e,n;return e=new rt({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p:N,i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function pt(o){let e,n;return e=new J({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$slots:{iconLeft:[$t]},$$scope:{ctx:o}}}),e.$on("click",o[8]),{c(){w(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){const r={};262144&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function ut(o){let e;return{c(){e=S("Trigger:")},m(n,t){x(n,e,t)},d(n){n&&y(e)}}}function mt(o){let e;return{c(){e=S("Open file")},m(n,t){x(n,e,t)},d(n){n&&y(e)}}}function dt(o){let e,n;return e=new D({props:{slot:"text",size:1,$$slots:{default:[mt]},$$scope:{ctx:o}}}),{c(){w(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){const r={};262144&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function ft(o){let e,n,t,s,r,c,$,l,f,a,p;return s=new at({props:{content:"Navigate back to all Rules & Guidelines",$$slots:{default:[pt]},$$scope:{ctx:o}}}),c=new D({props:{size:1,class:"c-field-label",$$slots:{default:[ut]},$$scope:{ctx:o}}}),l=new ot({props:{onSave:o[6],rule:o[4]}}),a=new X({props:{size:1,path:o[2],onOpenLocalFile:o[10],$$slots:{text:[dt]},$$scope:{ctx:o}}}),{c(){e=b("div"),n=b("div"),t=b("div"),w(s.$$.fragment),r=T(),w(c.$$.fragment),$=T(),w(l.$$.fragment),f=T(),w(a.$$.fragment),F(t,"class","c-trigger-section svelte-1r8al3d"),F(n,"class","l-file-controls-left svelte-1r8al3d"),F(e,"class","l-file-controls svelte-1r8al3d"),F(e,"slot","header")},m(i,u){x(i,e,u),R(e,n),R(n,t),h(s,t,null),R(t,r),h(c,t,null),R(t,$),h(l,t,null),R(e,f),h(a,e,null),p=!0},p(i,u){const C={};262144&u&&(C.$$scope={dirty:u,ctx:i}),s.$set(C);const g={};262144&u&&(g.$$scope={dirty:u,ctx:i}),c.$set(g);const E={};16&u&&(E.rule=i[4]),l.$set(E);const z={};4&u&&(z.path=i[2]),4&u&&(z.onOpenLocalFile=i[10]),262144&u&&(z.$$scope={dirty:u,ctx:i}),a.$set(z)},i(i){p||(m(s.$$.fragment,i),m(c.$$.fragment,i),m(l.$$.fragment,i),m(a.$$.fragment,i),p=!0)},o(i){d(s.$$.fragment,i),d(c.$$.fragment,i),d(l.$$.fragment,i),d(a.$$.fragment,i),p=!1},d(i){i&&y(e),v(s),v(c),v(l),v(a)}}}function gt(o){let e,n,t;function s(c){o[14](c)}let r={saveFunction:o[13],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[ft],default:[it]},$$scope:{ctx:o}};return o[0]!==void 0&&(r.value=o[0]),e=new nt({props:r}),B.push(()=>Q(e,"value",s)),{c(){w(e.$$.fragment)},m(c,$){h(e,c,$),t=!0},p(c,[$]){const l={};10&$&&(l.saveFunction=c[13]),262174&$&&(l.$$scope={dirty:$,ctx:c}),!n&&1&$&&(n=!0,l.value=c[0],U(()=>n=!1)),e.$set(l)},i(c){t||(m(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){v(e,c)}}}function vt(o,e,n){let t,s,r,{rule:c}=e,$=c.content,l=c.description;const f=new I(M),a=new Z,p=new tt(M,f,a),i=new st(f),u=async(g,E)=>{n(9,c={...c,type:g,description:E||l}),E!==void 0&&n(1,l=E);try{await i.updateRuleContent({type:g,path:t,content:$,description:E||l})}catch(z){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",z)}},C=ct.debounce(u,500);return o.$$set=g=>{"rule"in g&&n(9,c=g.rule)},o.$$.update=()=>{512&o.$$.dirty&&n(2,t=c.path),512&o.$$.dirty&&n(3,s=c.type),15&o.$$.dirty&&n(4,r={path:t,type:s,content:$,description:l})},[$,l,t,s,r,p,u,C,()=>{M.postMessage({type:L.openSettingsPage,data:"guidelines"})},c,async()=>(p.openFile({repoRoot:"",pathName:t}),"success"),function(g){l=g,n(1,l)},()=>C(s,l),()=>u(s,l),function(g){$=g,n(0,$)}]}class ht extends O{constructor(e){super(),W(this,e,vt,gt,j,{rule:9})}}function wt(o){let e;return{c(){e=b("div"),e.textContent="Loading..."},m(n,t){x(n,e,t)},p:N,i:N,o:N,d(n){n&&y(e)}}}function yt(o){let e,n;return e=new ht({props:{rule:o[0]}}),{c(){w(e.$$.fragment)},m(t,s){h(e,t,s),n=!0},p(t,s){const r={};1&s&&(r.rule=t[0]),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function xt(o){let e,n,t,s,r,c;const $=[yt,wt],l=[];function f(a,p){return a[0]!==null?0:1}return n=f(o),t=l[n]=$[n](o),{c(){e=b("div"),t.c(),F(e,"class","c-rules-container svelte-1vbu0zh")},m(a,p){x(a,e,p),l[n].m(e,null),s=!0,r||(c=P(window,"message",o[1].onMessageFromExtension),r=!0)},p(a,[p]){let i=n;n=f(a),n===i?l[n].p(a,p):(_(),d(l[i],1,1,()=>{l[i]=null}),q(),t=l[n],t?t.p(a,p):(t=l[n]=$[n](a),t.c()),m(t,1),t.m(e,null))},i(a){s||(m(t),s=!0)},o(a){d(t),s=!1},d(a){a&&y(e),l[n].d(),r=!1,c()}}}function Et(o,e,n){let t;const s=new I(M),r=Y(null);V(o,r,$=>n(0,t=$));const c={handleMessageFromExtension($){const l=$.data;if(l&&l.type===L.loadFile&&l){const f=l.data.content;if(f!==void 0){const a=f.replace(/^\n+/,""),p=k.getDescriptionFrontmatterKey(a),i=k.getRuleTypeFromContent(a),u=k.extractContent(a);r.set({path:l.data.pathName,content:u,type:i,description:p})}}return!0}};return H(()=>{s.registerConsumer(c),M.postMessage({type:L.rulesLoaded})}),[t,s,r]}new class extends O{constructor(o){super(),W(this,o,Et,xt,j,{})}}({target:document.getElementById("app")});

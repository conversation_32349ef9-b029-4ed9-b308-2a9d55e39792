#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment AI 直接API调用脚本
绕过UI界面直接与Augment AI交互
"""

import requests
import json
import time
import subprocess
import platform
import re
from typing import Optional, Dict, Any, List

class AugmentDirectAPI:
    def __init__(self, base_url: str = None):
        self.base_url = base_url or self.discover_api_port()
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AugmentDirectAPI/1.0'
        })
        print(f"🚀 初始化Augment API客户端: {self.base_url}")
    
    def discover_api_port(self) -> str:
        """自动发现IntelliJ IDEA的HTTP API端口"""
        print("🔍 正在查找IntelliJ IDEA HTTP API端口...")
        
        # 常用端口列表
        common_ports = [63342, 63343, 63344, 63345]
        
        # 尝试从netstat获取端口信息
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
                output = result.stdout
                
                # 查找监听的端口
                for port in common_ports:
                    if f":{port}" in output and "LISTENING" in output:
                        print(f"✅ 发现端口: {port}")
                        return f"http://localhost:{port}"
            else:
                # Linux/Mac
                for port in common_ports:
                    result = subprocess.run(['lsof', '-i', f':{port}'], capture_output=True, text=True)
                    if result.returncode == 0 and 'java' in result.stdout.lower():
                        print(f"✅ 发现端口: {port}")
                        return f"http://localhost:{port}"
        except Exception as e:
            print(f"⚠️ 端口发现失败: {e}")
        
        # 默认端口
        default_port = 63342
        print(f"🔧 使用默认端口: {default_port}")
        return f"http://localhost:{default_port}"
    
    def test_connection(self) -> bool:
        """测试API连接"""
        print("🔗 测试API连接...")
        try:
            # 尝试访问基本API端点
            response = self.session.get(f"{self.base_url}/api/about", timeout=5)
            if response.status_code == 200:
                print("✅ API连接成功")
                return True
            else:
                print(f"⚠️ API响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            return False
    
    def send_message_direct(self, text: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """直接发送消息给AI（方法1：直接API）"""
        print(f"📤 发送消息: {text[:50]}...")
        
        payload = {
            "text": text,
            "chatHistory": kwargs.get('chat_history', []),
            "modelId": kwargs.get('model_id', 'default'),
            "disableRetrieval": kwargs.get('disable_retrieval', False),
            "silent": kwargs.get('silent', False)
        }
        
        endpoints = [
            "/api/augment/chat/direct",
            "/api/augment/chat",
            "/api/chat/message"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"🎯 尝试端点: {endpoint}")
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print("✅ 消息发送成功")
                    return response.json()
                else:
                    print(f"⚠️ 端点响应: {response.status_code} - {response.text[:100]}")
                    
            except Exception as e:
                print(f"❌ 端点失败: {e}")
                continue
        
        return None
    
    def send_message_async(self, text: str) -> Optional[Dict[Any, Any]]:
        """通过异步包装器发送消息（方法2：异步API）"""
        print(f"📨 异步发送消息: {text[:50]}...")
        
        request_id = str(int(time.time() * 1000))
        payload = {
            "requestId": request_id,
            "baseMsg": {
                "@type": "type.googleapis.com/ChatUserMessageRequest",
                "data": {
                    "text": text,
                    "chatHistory": [],
                    "modelId": "default"
                }
            }
        }
        
        endpoints = [
            "/api/messaging/async",
            "/api/webview/message",
            "/api/augment/async"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"🎯 尝试异步端点: {endpoint}")
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print("✅ 异步消息发送成功")
                    return response.json()
                else:
                    print(f"⚠️ 异步端点响应: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 异步端点失败: {e}")
                continue
        
        return None
    
    def send_message_grpc_style(self, text: str) -> Optional[Dict[Any, Any]]:
        """模拟gRPC风格的消息发送（方法3：gRPC风格）"""
        print(f"🔧 gRPC风格发送: {text[:50]}...")
        
        payload = {
            "service": "WebviewChatService",
            "method": "ChatUserMessage",
            "request": {
                "data": {
                    "text": text,
                    "chatHistory": [],
                    "modelId": "default",
                    "disableRetrieval": False
                }
            }
        }
        
        endpoints = [
            "/api/grpc/WebviewChatService/ChatUserMessage",
            "/api/service/chat",
            "/api/rpc/chat"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"🎯 尝试gRPC端点: {endpoint}")
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print("✅ gRPC风格消息发送成功")
                    return response.json()
                else:
                    print(f"⚠️ gRPC端点响应: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ gRPC端点失败: {e}")
                continue
        
        return None
    
    def send_message_all_methods(self, text: str) -> Optional[Dict[Any, Any]]:
        """尝试所有方法发送消息"""
        print(f"🚀 尝试所有方法发送消息: {text}")
        print("=" * 60)
        
        # 方法1：直接API
        result = self.send_message_direct(text)
        if result:
            return result
        
        print("\n" + "-" * 40 + "\n")
        
        # 方法2：异步API
        result = self.send_message_async(text)
        if result:
            return result
        
        print("\n" + "-" * 40 + "\n")
        
        # 方法3：gRPC风格
        result = self.send_message_grpc_style(text)
        if result:
            return result
        
        print("\n❌ 所有方法都失败了")
        return None

def main():
    """主函数"""
    print("🤖 Augment AI 直接API调用工具")
    print("=" * 50)
    
    # 初始化API客户端
    api = AugmentDirectAPI()
    
    # 测试连接
    if not api.test_connection():
        print("❌ 无法连接到Augment API，请确保：")
        print("   1. IntelliJ IDEA正在运行")
        print("   2. Augment插件已安装并启用")
        print("   3. 防火墙允许本地连接")
        return
    
    print("\n" + "=" * 50)
    
    # 测试消息
    test_messages = [
        "Hello, Augment AI!",
        "解释一下这个项目的结构",
        "帮我分析当前代码的复杂度",
        "有什么可以优化的地方吗？"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n🧪 测试消息 {i}/{len(test_messages)}")
        print("=" * 60)
        
        result = api.send_message_all_methods(message)
        
        if result:
            print(f"\n✅ 成功收到回复:")
            print(f"📝 {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"\n❌ 消息发送失败")
        
        print("\n" + "🔄 等待3秒后继续...")
        time.sleep(3)
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()

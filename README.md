# Augment AI 直接API调用工具

这些脚本可以绕过Augment插件的UI界面，直接通过API与AI进行交互。

## 📁 文件说明

- `augment_direct_api.py` - Python版本的完整API客户端
- `augment_send_message.sh` - Bash脚本版本，适用于Linux/Mac/Windows(WSL)
- `augment_api_client.js` - Node.js版本的API客户端
- `README.md` - 使用说明文档

## 🚀 使用方法

### Python版本

```bash
# 安装依赖
pip install requests

# 运行脚本（自动测试多条消息）
python augment_direct_api.py

# 或者修改脚本中的test_messages来自定义消息
```

### Bash版本

```bash
# 给脚本执行权限
chmod +x augment_send_message.sh

# 发送单条消息
./augment_send_message.sh "解释这个项目的结构"

# 发送其他消息
./augment_send_message.sh "帮我分析代码复杂度"
./augment_send_message.sh "有什么可以优化的地方吗？"
```

### Node.js版本

```bash
# 安装依赖
npm install axios

# 发送消息
node augment_api_client.js "Hello, Augment AI!"
node augment_api_client.js "解释这个项目的结构"
```

## 🔧 工作原理

这些脚本通过以下方式尝试与Augment AI通信：

### 1. 端口发现
- 自动扫描常用端口：63342, 63343, 63344, 63345
- 使用netstat/lsof查找IntelliJ IDEA的HTTP API端口

### 2. 多种API调用方式
- **直接API**: `/api/augment/chat/direct`
- **异步API**: `/api/messaging/async`
- **WebView消息**: `/api/webview/message`
- **gRPC风格**: `/api/grpc/WebviewChatService/ChatUserMessage`

### 3. 消息格式
```json
{
  "text": "你的消息内容",
  "chatHistory": [],
  "modelId": "default",
  "disableRetrieval": false,
  "silent": false
}
```

## ⚠️ 前置条件

1. **IntelliJ IDEA正在运行**
2. **Augment插件已安装并启用**
3. **Augment插件已登录**
4. **防火墙允许本地连接**

## 🔍 故障排除

### 连接失败
```bash
# 检查IntelliJ IDEA是否运行
ps aux | grep idea

# 检查端口是否监听
netstat -an | grep :63342
lsof -i :63342

# 检查防火墙
# Windows: 检查Windows防火墙设置
# Linux: sudo ufw status
# Mac: 系统偏好设置 > 安全性与隐私 > 防火墙
```

### API调用失败
1. **检查IDE日志**
   - Help > Show Log in Explorer
   - 查找Augment相关错误

2. **确认插件状态**
   - File > Settings > Plugins
   - 确保Augment插件已启用

3. **重启IDE**
   - 完全关闭IntelliJ IDEA
   - 重新启动并等待插件加载

### 调试模式
在脚本中添加详细日志输出，查看具体的HTTP响应：

```bash
# Bash版本 - 添加详细输出
curl -v -X POST -H "Content-Type: application/json" \
  -d '{"text":"test"}' \
  http://localhost:63342/api/augment/chat
```

## 🎯 高级用法

### 静默消息（不显示在UI中）
```python
api.send_message_direct("后台分析代码", silent=True)
```

### 指定模型
```python
api.send_message_direct("解释代码", model_id="gpt-4")
```

### 带历史记录的对话
```python
chat_history = [
    {"role": "user", "content": "什么是Python?"},
    {"role": "assistant", "content": "Python是一种编程语言..."}
]
api.send_message_direct("继续解释", chat_history=chat_history)
```

## 📝 API响应格式

成功响应示例：
```json
{
  "requestId": "1234567890",
  "status": "success",
  "data": {
    "text": "AI的回复内容...",
    "model": "default",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

错误响应示例：
```json
{
  "error": "Authentication required",
  "status": "error",
  "code": 401
}
```

## 🔐 安全注意事项

1. **本地访问**: 这些API只能从本地访问
2. **认证**: 可能需要有效的session或token
3. **权限**: 确保有足够的系统权限访问网络端口

## 📞 支持

如果遇到问题：
1. 检查IntelliJ IDEA和Augment插件版本
2. 查看IDE日志文件
3. 确认网络和防火墙设置
4. 尝试重启IDE和插件

## 🎉 成功示例

当脚本成功运行时，你会看到类似输出：
```
🤖 Augment AI 直接API调用工具
==================================================
🔍 正在查找IntelliJ IDEA HTTP API端口...
✅ 发现端口: 63342
🚀 初始化Augment API客户端: http://localhost:63342
🔗 测试API连接...
✅ API连接成功

📤 发送消息: 解释这个项目的结构...
🎯 尝试端点: /api/augment/chat/direct
✅ 消息发送成功
📝 AI回复: 这个项目是一个...
```

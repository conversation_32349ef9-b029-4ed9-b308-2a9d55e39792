import{p as E}from"./chunk-TMUBEWPD-DmR2TQaW.js";import{T as y,O,aF as L,_ as m,g as N,s as P,a as V,b as G,o as I,p as _,l as F,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-bvgL2GJ0.js";import{p as Y}from"./gitGraph-YCYPL57B-w4_HHwiS.js";import{d as B}from"./arc-qH831bWR.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BejxPZX4.js";import"./IconButtonAugment-DmfIVAYG.js";import"./CalloutAugment-DH8_XiUe.js";import"./CardAugment-dQY7gB6x.js";import"./index-LNVO_dOZ.js";import"./async-messaging-D7f6isRQ.js";import"./message-broker-B_Xbv83k.js";import"./types-CGlLNakm.js";import"./file-paths-B4wu8Zer.js";import"./BaseTextInput-B8Zm7GJJ.js";import"./folder-opened-ChvTYBfK.js";import"./index-BlaqVU85.js";import"./diff-operations-BLNMYUZC.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-DSk2r097.js";import"./index-6AlVWKh5.js";import"./keypress-DD1aQVr0.js";import"./await_block-Dt1rTGTc.js";import"./OpenFileButton-DltEK_xL.js";import"./chat-context-CmoAn9pE.js";import"./index-B528snJk.js";import"./remote-agents-client-DwCOg82s.js";import"./ra-diff-ops-model-DHtiwZCr.js";import"./TextAreaAugment-DCLSWWW0.js";import"./ButtonAugment-CVatBELe.js";import"./CollapseButtonAugment-03DKN6zM.js";import"./partner-mcp-utils-PxcJW8dJ.js";import"./MaterialIcon-CUEAtZVx.js";import"./CopyButton-Cb58npax.js";import"./copy-BA1J_YQn.js";import"./ellipsis-BRv5sK55.js";import"./IconFilePath-BUMTFdYT.js";import"./LanguageIcon-B9tL-FlO.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-DMucGkIH.js";import"./index-Dkoz80k5.js";import"./augment-logo-BRK9m6wM.js";import"./pen-to-square-5PNRCAQu.js";import"./chevron-down-D78W1-g3.js";import"./check-Cc9E51ln.js";import"./_baseUniq-Dec7a2Hj.js";import"./_basePickBy-DktbUoK0.js";import"./clone-C4dhdsvk.js";import"./init-g68aIKmP.js";function tt(t,a){return a<t?-1:a>t?1:a>=t?0:NaN}function et(t){return t}var at=X.pie,R={sections:new Map,showData:!1},M=R.sections,z=R.showData,rt=structuredClone(at),W={getConfig:m(()=>structuredClone(rt),"getConfig"),clear:m(()=>{M=new Map,z=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:P,getAccDescription:N,addSection:m(({label:t,value:a})=>{M.has(t)||(M.set(t,a),F.debug(`added new section: ${t}, with value: ${a}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},it=m((t,a)=>{E(t,a),a.setShowData(t.showData),t.sections.map(a.addSection)},"populateDb"),nt={parse:m(async t=>{const a=await Y("pie",t);F.debug(a),it(a,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const a=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function r(e){var i,s,n,T,g,l=(e=L(e)).length,v=0,A=new Array(l),d=new Array(l),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/l,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<l;++i)(g=d[A[i]=i]=+p(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-l*b)/v:0;i<l;++i,f=T)s=A[i],T=f+((g=d[s])>0?g*n:0)+b,d[s]={data:e[s],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return r.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),r):p},r.sortValues=function(e){return arguments.length?(u=e,c=null,r):u},r.sort=function(e){return arguments.length?(c=e,u=null,r):c},r.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),r):w},r.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),r):S},r.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),r):$},r}().value(p=>p.value)(a)},"createPieArcs"),ie={parser:nt,db:W,renderer:{draw:m((t,a,p,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,r=450,e=r,i=J(a),s=i.append("g");s.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,l=Math.min(e,r)/2-40,v=B().innerRadius(0).outerRadius(l),A=B().innerRadius(l*g).outerRadius(l*g);s.append("circle").attr("cx",0).attr("cy",0).attr("r",l+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);s.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),s.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),s.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=s.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,r,D,S.useMaxWidth)},"draw")},styles:ot};export{ie as diagram};

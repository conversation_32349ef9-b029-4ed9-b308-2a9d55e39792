var Le=Object.defineProperty;var ne=c=>{throw TypeError(c)};var Ue=(c,t,e)=>t in c?Le(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e;var r=(c,t,e)=>Ue(c,typeof t!="symbol"?t+"":t,e),qt=(c,t,e)=>t.has(c)||ne("Cannot "+e);var i=(c,t,e)=>(qt(c,t,"read from private field"),e?e.call(c):t.get(c)),v=(c,t,e)=>t.has(c)?ne("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(c):t.set(c,e),f=(c,t,e,s)=>(qt(c,t,"write to private field"),s?s.call(c,e):t.set(c,e),e),u=(c,t,e)=>(qt(c,t,"access private method"),e);var Dt=(c,t,e,s)=>({set _(o){f(c,t,o,e)},get _(){return i(c,t,s)}});import{S as De,W as dt,e as oe}from"./IconButtonAugment-DmfIVAYG.js";import{d as Re}from"./CardAugment-dQY7gB6x.js";import{A as tt,S as wt,i as xt,s as Mt,a as St,n as P,d as et,b as Wt,g as Se,u as we,v as xe,w as Me,x as Te,f as ht,H as Fe,j as Vt,a7 as Pe,a8 as ae,h as C,c as kt,F as Ae,X as ze,e as It,Y as Ge,V as He}from"./SpinnerAugment-BejxPZX4.js";import{i as At,S as Pt,c as _t,j as Ie,k as re,C as Ne,E as We,l as lt,m as Ve,n as je,o as qe,s as Bt,N as ct,p as $t,q as Be,r as $e,t as he,u as Ze,v as le,w as ce,x as Ke,y as de,z as Je,B as ue,G as E,H as Ye,I as fe,J as Xe,K as Zt,L as Qe}from"./index-LNVO_dOZ.js";import{C as ts,a as es}from"./message-broker-B_Xbv83k.js";import{i as ss}from"./file-paths-B4wu8Zer.js";class is{constructor(t=!0,e=setTimeout){r(this,"_notify",new Set);r(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});r(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});r(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});r(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,o){if(!t&&!o)return()=>{};const n={timeout:t,notify:e,once:s,date:o};return this._notify.add(n),this._schedule(n),()=>{this._clearTimeout(n),this._notify.delete(n)}}}class ns{constructor(t=0,e=0,s=new is,o=tt("busy"),n=tt(!1)){r(this,"unsubNotify");r(this,"unsubMessage");r(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});r(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=o,this.focusAfterIdle=n,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}function os(c){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},c[0]],o={};for(let n=0;n<s.length;n+=1)o=St(o,s[n]);return{c(){t=ht("svg"),e=new Fe(!0),this.h()},l(n){t=xe(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=Me(t);e=Te(a,!0),a.forEach(et),this.h()},h(){e.a=null,Wt(t,o)},m(n,a){we(n,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3L266 249.3c3.4.4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6c-9.4 1.2-17.6 6.9-22 15.3l-63 121.2c-17.4 33.5 17 70.2 51.6 55.1l435.2-190.9c25.5-11.2 25.5-47.4 0-58.6z"/>',t)},p(n,[a]){Wt(t,o=Se(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&n[0]]))},i:P,o:P,d(n){n&&et(t)}}}function as(c,t,e){return c.$$set=s=>{e(0,t=St(St({},t),Vt(s)))},[t=Vt(t)]}function rs(c){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},c[0]],o={};for(let n=0;n<s.length;n+=1)o=St(o,s[n]);return{c(){t=ht("svg"),e=new Fe(!0),this.h()},l(n){t=xe(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=Me(t);e=Te(a,!0),a.forEach(et),this.h()},h(){e.a=null,Wt(t,o)},m(n,a){we(n,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',t)},p(n,[a]){Wt(t,o=Se(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&n[0]]))},i:P,o:P,d(n){n&&et(t)}}}function hs(c,t,e){return c.$$set=s=>{e(0,t=St(St({},t),Vt(s)))},[t=Vt(t)]}class ls extends wt{constructor(t){super(),xt(this,t,hs,rs,Mt,{})}}var zt=(c=>(c.send="send",c.addTask="addTask",c))(zt||{});const cs={id:"send",label:"Send to Agent",icon:class extends wt{constructor(c){super(),xt(this,c,as,os,Mt,{})}},description:"Send message to agent"},Os=[cs,{id:"addTask",label:"Add Task",icon:ls,description:"Add task with the message content"}];class ds{constructor(){r(this,"_mode",tt(zt.send));r(this,"_currentMode",zt.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(zt).includes(t)&&this._mode.set(t)}}class us{constructor(t=3e5){r(this,"_cleanItems",new Set);r(this,"_lastProcessedTime",new Map);this.cooldownMs=t}markClean(t){this._cleanItems.add(t),this._lastProcessedTime.set(t,Date.now())}markDirty(t){this._cleanItems.delete(t),this._lastProcessedTime.delete(t)}isClean(t){return this._cleanItems.has(t)}isWithinCooldown(t){const e=this._lastProcessedTime.get(t);return!!e&&Date.now()-e<this.cooldownMs}getLastProcessedTime(t){return this._lastProcessedTime.get(t)||0}cleanup(t){const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this.markDirty(s)}clear(){this._cleanItems.clear(),this._lastProcessedTime.clear()}getStats(){return{cleanCount:this._cleanItems.size,trackedCount:this._lastProcessedTime.size}}}const L=[];for(let c=0;c<256;++c)L.push((c+256).toString(16).slice(1));let Kt;const fs=new Uint8Array(16),ge={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function gs(c,t,e){var o;if(ge.randomUUID&&!c)return ge.randomUUID();const s=(c=c||{}).random??((o=c.rng)==null?void 0:o.call(c))??function(){if(!Kt){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Kt=crypto.getRandomValues.bind(crypto)}return Kt(fs)}();if(s.length<16)throw new Error("Random bytes length must be >= 16");return s[6]=15&s[6]|64,s[8]=63&s[8]|128,function(n,a=0){return(L[n[a+0]]+L[n[a+1]]+L[n[a+2]]+L[n[a+3]]+"-"+L[n[a+4]]+L[n[a+5]]+"-"+L[n[a+6]]+L[n[a+7]]+"-"+L[n[a+8]]+L[n[a+9]]+"-"+L[n[a+10]]+L[n[a+11]]+L[n[a+12]]+L[n[a+13]]+L[n[a+14]]+L[n[a+15]]).toLowerCase()}(s)}class ms{constructor(t,e,s=new us(3e5)){r(this,"_maxItemsPerIteration",5);this._extensionClient=t,this._flags=e,this._cache=s}async hydrateConversation(t){let e=t;return e=await this._hydrateExchanges(e),e=await this._hydrateToolUseStates(e),this._cache.markDirty(t.id),e}async dehydrateConversation(t){let e=t;return this._flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this._flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async dehydrateConversationsIncremental(t,e){if(!this._flags.enableExchangeStorage&&!this._flags.enableToolUseStateStorage)return t;const s=Object.entries(t),o=this._selectConversationsForDehydration(s,e),n={};for(const[a,h]of s)if(o.includes(a))try{const l=await this.dehydrateConversation(h);this._cache.markClean(a),n[a]=a===e?h:l}catch(l){console.warn(`Failed to dehydrate conversation ${a}:`,l),n[a]=h}else n[a]=h;return n}async hydrateCurrentConversation(t,e){if(!e||!t[e])return t;try{const s=await this.hydrateConversation(t[e]);return{...t,[e]:s}}catch(s){return console.warn(`Failed to hydrate conversation ${e}:`,s),t}}markNeedsDehydration(t){this._cache.markDirty(t)}markDehydrated(t){this._cache.markClean(t)}cleanupDeletedConversations(t){this._cache.cleanup(t)}_selectConversationsForDehydration(t,e){var n;const s=[];if(e){const a=(n=t.find(([h])=>h===e))==null?void 0:n[1];a&&!this._cache.isClean(e)&&(a.chatHistory.some(At)?s.push(e):this._cache.markClean(e))}const o=t.filter(([a,h])=>a===e||this._cache.isClean(a)?!1:h.chatHistory.some(At)?!this._cache.isWithinCooldown(a):(this._cache.markClean(a),!1)).map(([a])=>a).sort((a,h)=>this._cache.getLastProcessedTime(a)-this._cache.getLastProcessedTime(h));return[...s,...o].slice(0,this._maxItemsPerIteration)}async _dehydrateExchanges(t){var o;const e=[],s=[];for(const n of t.chatHistory)if(At(n)){const a=n,h=a.request_id||((o=crypto==null?void 0:crypto.randomUUID)==null?void 0:o.call(crypto))||gs(),l={request_message:a.request_message,response_text:a.response_text||"",request_id:h,request_nodes:a.structured_request_nodes,response_nodes:a.structured_output_nodes,uuid:h,conversationId:t.id,status:a.status===_t.success?"success":a.status===_t.failed?"failed":"sent",timestamp:a.timestamp||new Date().toISOString(),seen_state:a.seen_state===Pt.seen?"seen":"unseen"};e.push(l);const g={chatItemType:Ie.exchangePointer,exchangeUuid:h,timestamp:a.timestamp,request_message:a.request_message,status:a.status,hasResponse:!!a.response_text,isStreaming:a.status===_t.sent,seen_state:a.seen_state};s.push(g)}else s.push(n);if(e.length>0)try{await this._extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this._flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this._extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateExchanges(t){const e=t.chatHistory.filter(re);if(e.length===0)return t;try{const s=e.map(h=>h.exchangeUuid),o=await this._extensionClient.loadExchanges(t.id,s),n=new Map(o.map(h=>[h.uuid,h])),a=t.chatHistory.map(h=>{if(re(h)){const l=n.get(h.exchangeUuid);if(l)return{request_message:l.request_message,response_text:l.response_text,request_id:l.request_id,structured_request_nodes:l.request_nodes,structured_output_nodes:l.response_nodes,timestamp:l.timestamp,status:l.status==="success"?_t.success:l.status==="failed"?_t.failed:_t.sent,seen_state:l.seen_state==="seen"?Pt.seen:Pt.unseen}}return h});return{...t,chatHistory:a}}catch(s){return console.warn(`Failed to restore exchanges for conversation ${t.id}:`,s),t}}async _hydrateToolUseStates(t){try{const e=await this._extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:{...t.toolUseStates,...e}}}catch(e){return console.warn(`Failed to restore tool use states for conversation ${t.id}:`,e),t}}}const Rt=tt("idle");var _s=(c=>(c.manual="manual",c.auto="auto",c))(_s||{});class ps{constructor(t,e,s,o={}){r(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});r(this,"extensionClient");r(this,"_chatFlagsModel");r(this,"_currConversationModel");r(this,"_chatModeModel");r(this,"_sendModeModel");r(this,"_flagsLoaded",tt(!1));r(this,"_persistenceController");r(this,"subscribers",new Set);r(this,"idleMessageModel",new ns);r(this,"isPanelCollapsed");r(this,"agentExecutionMode");r(this,"sortConversationsBy");r(this,"displayedAnnouncements");r(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??qe,bigSyncThreshold:t.bigSyncThreshold??je,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Ve,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??De.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{}}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});r(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));r(this,"initializeSync",t=>{if(this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Bt&&this.currentConversationId!==Bt||(delete this._state.conversations[Bt],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===ct||lt.isValid(s))),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});r(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const o=s.chatHistory.some(n=>$t(n));t[e]={...s,isShareable:o}}this._state.conversations=t});r(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[o,n]of Object.entries(e))n.isPinned&&s.add(o);this.setState(this._state),this.notifySubscribers()});r(this,"saveImmediate",()=>{this.setState(this._state),this.setState.flush()});r(this,"setState");r(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});r(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));r(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Ie.educateFeatures,request_id:crypto.randomUUID(),seen_state:Pt.seen})});r(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});r(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let o;t===void 0&&(t=ct);const n=this._state.conversations[t];o=n?await this._persistenceController.hydrateConversation(n):lt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===ct&&(o.id=ct),s!=null&&s.newTaskUuid&&(o.rootTaskUuid=s.newTaskUuid);const a=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(o,!a,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});r(this,"saveConversation",async(t,e)=>{this._persistenceController.markNeedsDehydration(t.id),this.updateChatState({...this._state,currentConversationId:t.id,conversations:{...this._state.conversations,[t.id]:t}}),e&&delete this._state.conversations[ct]});r(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});r(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});r(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;Rt.set("copying");const s=e==null?void 0:e.chatHistory,o=s.reduce((h,l)=>($t(l)&&h.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),h),[]);if(o.length===0)throw new Error("No chat history to share");const n=lt.getDisplayName(e),a=await this.extensionClient.saveChat(t,o,n);if(a.data){let h=a.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:h}}}),h}throw new Error("Failed to create URL")});r(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void Rt.set("idle");navigator.clipboard.writeText(e),Rt.set("copied")}catch{Rt.set("failed")}});r(this,"deleteConversations",async(t,e=void 0,s=[],o)=>{const n=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${n>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const a=new Set(t);await this.deleteConversationIds(a)}if(s.length>0&&o)for(const a of s)try{await o.deleteAgent(a,!0)}catch(h){console.error(`Failed to delete remote agent ${a}:`,h)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});r(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});r(this,"deleteConversationIds",async t=>{var o,n,a;const e=[],s=[];for(const h of t){const l=((o=this._state.conversations[h])==null?void 0:o.requestIds)??[];e.push(...l);const g=((n=this._state.conversations[h])==null?void 0:n.toolUseStates)??{};for(const p of Object.keys(g)){const{toolUseId:O}=g[p];O&&s.push(O)}const b=this._state.conversations[h];if(b){for(const p of b.chatHistory)if(At(p)&&p.structured_output_nodes)for(const O of p.structured_output_nodes)O.type===ts.TOOL_USE&&((a=O.tool_use)!=null&&a.tool_use_id)&&s.push(O.tool_use.tool_use_id)}}for(const h of Object.values(this._state.conversations))if(t.has(h.id)){for(const g of h.chatHistory)At(g)&&this.deleteImagesInExchange(g);const l=h.draftExchange;l&&this.deleteImagesInExchange(l)}for(const h of t){try{await this.extensionClient.deleteConversationExchanges(h)}catch(l){console.error(`Failed to delete exchanges for conversation ${h}:`,l)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(h)}catch(l){console.error(`Failed to delete tool use states for conversation ${h}:`,l)}}this._persistenceController.cleanupDeletedConversations(t),this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([h])=>!t.has(h)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});r(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});r(this,"findImagesInJson",t=>{const e=[],s=o=>{var n,a;if(o.type==="file"&&((n=o.attrs)!=null&&n.src)){const h=(a=o.attrs)==null?void 0:a.src;ss(h)&&e.push(o.attrs.src)}else if((o.type==="doc"||o.type==="paragraph")&&o.content)for(const h of o.content)s(h)};return s(t),e});r(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===es.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));r(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});r(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});r(this,"smartPaste",(t,e,s,o)=>{const n=this._currConversationModel.historyTo(t,!0).filter(a=>$t(a)).map(a=>({request_message:a.request_message,response_text:a.response_text||"",request_id:a.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:n,targetFile:s??void 0,options:o})});r(this,"saveImage",async t=>await this.extensionClient.saveImage(t));r(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));r(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));r(this,"renderImage",async t=>await this.extensionClient.loadImage(t));var l,g;this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=o,this._chatFlagsModel=new Ne(o.initialFlags),this.extensionClient=new We(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new lt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new ds,this._persistenceController=new ms(this.extensionClient,this._chatFlagsModel);const n=((l=o.debounceConfig)==null?void 0:l.wait)??5e3,a=((g=o.debounceConfig)==null?void 0:g.maxWait)??3e4;this.setState=Re(b=>{this._setStateWithPersistence(b)},n,{maxWait:a}),this.initializeSync(o.initialConversation);const h=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=tt(h),this.agentExecutionMode=tt(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=tt(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=tt(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flagsLoaded(){return this._flagsLoaded}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;this._state.conversations=await this._persistenceController.hydrateCurrentConversation(this._state.conversations,e),t?await this.setCurrentConversation(t.id):await this.setCurrentConversation(this.currentConversationId)}async _setStateWithPersistence(t){const e=await this._persistenceController.dehydrateConversationsIncremental(t.conversations??this._state.conversations,this.currentConversationId);this._host.setState({...t,conversations:e})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const o=t||this._state.sortConversationsBy||"lastMessageTimestamp";let n=Object.values(this._state.conversations);return s&&(n=n.filter(s)),n.sort((a,h)=>{const l=lt.getTime(a,o).getTime(),g=lt.getTime(h,o).getTime();return e==="asc"?l-g:g-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===ct)return!1;const o=!lt.isValid(this.conversations[s]),n=Be(this.conversations[s]);return o&&(t==="agent"&&n||t==="chat"&&!n||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===dt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}r(ps,"NEW_AGENT_KEY",ct);const pt=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,me=new Set,Jt=typeof process=="object"&&process?process:{},Ee=(c,t,e,s)=>{typeof Jt.emitWarning=="function"?Jt.emitWarning(c,t,e,s):console.error(`[${e}] ${t}: ${c}`)};let jt=globalThis.AbortController,_e=globalThis.AbortSignal;var Ce;if(jt===void 0){_e=class{constructor(){r(this,"onabort");r(this,"_onabort",[]);r(this,"reason");r(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},jt=class{constructor(){r(this,"signal",new _e);t()}abort(e){var s,o;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const n of this.signal._onabort)n(e);(o=(s=this.signal).onabort)==null||o.call(s,e)}}};let c=((Ce=Jt.env)==null?void 0:Ce.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{c&&(c=!1,Ee("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const it=c=>c&&c===Math.floor(c)&&c>0&&isFinite(c),Oe=c=>it(c)?c<=Math.pow(2,8)?Uint8Array:c<=Math.pow(2,16)?Uint16Array:c<=Math.pow(2,32)?Uint32Array:c<=Number.MAX_SAFE_INTEGER?Gt:null:null;class Gt extends Array{constructor(t){super(t),this.fill(0)}}var vt;const ut=class ut{constructor(t,e){r(this,"heap");r(this,"length");if(!i(ut,vt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=Oe(t);if(!e)return[];f(ut,vt,!0);const s=new ut(t,e);return f(ut,vt,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};vt=new WeakMap,v(ut,vt,!1);let Yt=ut;var be,ye,j,G,q,B,Ct,bt,T,$,M,S,_,D,H,U,A,Z,I,K,J,N,Y,rt,R,d,Qt,ft,Q,Et,W,ke,gt,yt,Ot,nt,ot,te,Ht,Nt,y,ee,Ft,at,se;const ie=class ie{constructor(t){v(this,d);v(this,j);v(this,G);v(this,q);v(this,B);v(this,Ct);v(this,bt);r(this,"ttl");r(this,"ttlResolution");r(this,"ttlAutopurge");r(this,"updateAgeOnGet");r(this,"updateAgeOnHas");r(this,"allowStale");r(this,"noDisposeOnSet");r(this,"noUpdateTTL");r(this,"maxEntrySize");r(this,"sizeCalculation");r(this,"noDeleteOnFetchRejection");r(this,"noDeleteOnStaleGet");r(this,"allowStaleOnFetchAbort");r(this,"allowStaleOnFetchRejection");r(this,"ignoreFetchAbort");v(this,T);v(this,$);v(this,M);v(this,S);v(this,_);v(this,D);v(this,H);v(this,U);v(this,A);v(this,Z);v(this,I);v(this,K);v(this,J);v(this,N);v(this,Y);v(this,rt);v(this,R);v(this,ft,()=>{});v(this,Q,()=>{});v(this,Et,()=>{});v(this,W,()=>!1);v(this,gt,t=>{});v(this,yt,(t,e,s)=>{});v(this,Ot,(t,e,s,o)=>{if(s||o)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});r(this,be,"LRUCache");const{max:e=0,ttl:s,ttlResolution:o=1,ttlAutopurge:n,updateAgeOnGet:a,updateAgeOnHas:h,allowStale:l,dispose:g,disposeAfter:b,noDisposeOnSet:p,noUpdateTTL:O,maxSize:w=0,maxEntrySize:z=0,sizeCalculation:k,fetchMethod:F,memoMethod:m,noDeleteOnFetchRejection:x,noDeleteOnStaleGet:Lt,allowStaleOnFetchRejection:X,allowStaleOnFetchAbort:V,ignoreFetchAbort:Tt}=t;if(e!==0&&!it(e))throw new TypeError("max option must be a nonnegative integer");const st=e?Oe(e):Array;if(!st)throw new Error("invalid max value: "+e);if(f(this,j,e),f(this,G,w),this.maxEntrySize=z||i(this,G),this.sizeCalculation=k,this.sizeCalculation){if(!i(this,G)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(m!==void 0&&typeof m!="function")throw new TypeError("memoMethod must be a function if defined");if(f(this,bt,m),F!==void 0&&typeof F!="function")throw new TypeError("fetchMethod must be a function if specified");if(f(this,Ct,F),f(this,rt,!!F),f(this,M,new Map),f(this,S,new Array(e).fill(void 0)),f(this,_,new Array(e).fill(void 0)),f(this,D,new st(e)),f(this,H,new st(e)),f(this,U,0),f(this,A,0),f(this,Z,Yt.create(e)),f(this,T,0),f(this,$,0),typeof g=="function"&&f(this,q,g),typeof b=="function"?(f(this,B,b),f(this,I,[])):(f(this,B,void 0),f(this,I,void 0)),f(this,Y,!!i(this,q)),f(this,R,!!i(this,B)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!O,this.noDeleteOnFetchRejection=!!x,this.allowStaleOnFetchRejection=!!X,this.allowStaleOnFetchAbort=!!V,this.ignoreFetchAbort=!!Tt,this.maxEntrySize!==0){if(i(this,G)!==0&&!it(i(this,G)))throw new TypeError("maxSize must be a positive integer if specified");if(!it(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");u(this,d,ke).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!Lt,this.updateAgeOnGet=!!a,this.updateAgeOnHas=!!h,this.ttlResolution=it(o)||o===0?o:1,this.ttlAutopurge=!!n,this.ttl=s||0,this.ttl){if(!it(this.ttl))throw new TypeError("ttl must be a positive integer if specified");u(this,d,Qt).call(this)}if(i(this,j)===0&&this.ttl===0&&i(this,G)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!i(this,j)&&!i(this,G)){const mt="LRU_CACHE_UNBOUNDED";(Ut=>!me.has(Ut))(mt)&&(me.add(mt),Ee("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",mt,ie))}}static unsafeExposeInternals(t){return{starts:i(t,J),ttls:i(t,N),sizes:i(t,K),keyMap:i(t,M),keyList:i(t,S),valList:i(t,_),next:i(t,D),prev:i(t,H),get head(){return i(t,U)},get tail(){return i(t,A)},free:i(t,Z),isBackgroundFetch:e=>{var s;return u(s=t,d,y).call(s,e)},backgroundFetch:(e,s,o,n)=>{var a;return u(a=t,d,Nt).call(a,e,s,o,n)},moveToTail:e=>{var s;return u(s=t,d,Ft).call(s,e)},indexes:e=>{var s;return u(s=t,d,nt).call(s,e)},rindexes:e=>{var s;return u(s=t,d,ot).call(s,e)},isStale:e=>{var s;return i(s=t,W).call(s,e)}}}get max(){return i(this,j)}get maxSize(){return i(this,G)}get calculatedSize(){return i(this,$)}get size(){return i(this,T)}get fetchMethod(){return i(this,Ct)}get memoMethod(){return i(this,bt)}get dispose(){return i(this,q)}get disposeAfter(){return i(this,B)}getRemainingTTL(t){return i(this,M).has(t)?1/0:0}*entries(){for(const t of u(this,d,nt).call(this))i(this,_)[t]===void 0||i(this,S)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield[i(this,S)[t],i(this,_)[t]])}*rentries(){for(const t of u(this,d,ot).call(this))i(this,_)[t]===void 0||i(this,S)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield[i(this,S)[t],i(this,_)[t]])}*keys(){for(const t of u(this,d,nt).call(this)){const e=i(this,S)[t];e===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield e)}}*rkeys(){for(const t of u(this,d,ot).call(this)){const e=i(this,S)[t];e===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield e)}}*values(){for(const t of u(this,d,nt).call(this))i(this,_)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield i(this,_)[t])}*rvalues(){for(const t of u(this,d,ot).call(this))i(this,_)[t]===void 0||u(this,d,y).call(this,i(this,_)[t])||(yield i(this,_)[t])}[(ye=Symbol.iterator,be=Symbol.toStringTag,ye)](){return this.entries()}find(t,e={}){for(const s of u(this,d,nt).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;if(n!==void 0&&t(n,i(this,S)[s],this))return this.get(i(this,S)[s],e)}}forEach(t,e=this){for(const s of u(this,d,nt).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;n!==void 0&&t.call(e,n,i(this,S)[s],this)}}rforEach(t,e=this){for(const s of u(this,d,ot).call(this)){const o=i(this,_)[s],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;n!==void 0&&t.call(e,n,i(this,S)[s],this)}}purgeStale(){let t=!1;for(const e of u(this,d,ot).call(this,{allowStale:!0}))i(this,W).call(this,e)&&(u(this,d,at).call(this,i(this,S)[e],"expire"),t=!0);return t}info(t){const e=i(this,M).get(t);if(e===void 0)return;const s=i(this,_)[e],o=u(this,d,y).call(this,s)?s.__staleWhileFetching:s;if(o===void 0)return;const n={value:o};if(i(this,N)&&i(this,J)){const a=i(this,N)[e],h=i(this,J)[e];if(a&&h){const l=a-(pt.now()-h);n.ttl=l,n.start=Date.now()}}return i(this,K)&&(n.size=i(this,K)[e]),n}dump(){const t=[];for(const e of u(this,d,nt).call(this,{allowStale:!0})){const s=i(this,S)[e],o=i(this,_)[e],n=u(this,d,y).call(this,o)?o.__staleWhileFetching:o;if(n===void 0||s===void 0)continue;const a={value:n};if(i(this,N)&&i(this,J)){a.ttl=i(this,N)[e];const h=pt.now()-i(this,J)[e];a.start=Math.floor(Date.now()-h)}i(this,K)&&(a.size=i(this,K)[e]),t.unshift([s,a])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const o=Date.now()-s.start;s.start=pt.now()-o}this.set(e,s.value,s)}}set(t,e,s={}){var O,w,z,k,F;if(e===void 0)return this.delete(t),this;const{ttl:o=this.ttl,start:n,noDisposeOnSet:a=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:l}=s;let{noUpdateTTL:g=this.noUpdateTTL}=s;const b=i(this,Ot).call(this,t,e,s.size||0,h);if(this.maxEntrySize&&b>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),u(this,d,at).call(this,t,"set"),this;let p=i(this,T)===0?void 0:i(this,M).get(t);if(p===void 0)p=i(this,T)===0?i(this,A):i(this,Z).length!==0?i(this,Z).pop():i(this,T)===i(this,j)?u(this,d,Ht).call(this,!1):i(this,T),i(this,S)[p]=t,i(this,_)[p]=e,i(this,M).set(t,p),i(this,D)[i(this,A)]=p,i(this,H)[p]=i(this,A),f(this,A,p),Dt(this,T)._++,i(this,yt).call(this,p,b,l),l&&(l.set="add"),g=!1;else{u(this,d,Ft).call(this,p);const m=i(this,_)[p];if(e!==m){if(i(this,rt)&&u(this,d,y).call(this,m)){m.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:x}=m;x===void 0||a||(i(this,Y)&&((O=i(this,q))==null||O.call(this,x,t,"set")),i(this,R)&&((w=i(this,I))==null||w.push([x,t,"set"])))}else a||(i(this,Y)&&((z=i(this,q))==null||z.call(this,m,t,"set")),i(this,R)&&((k=i(this,I))==null||k.push([m,t,"set"])));if(i(this,gt).call(this,p),i(this,yt).call(this,p,b,l),i(this,_)[p]=e,l){l.set="replace";const x=m&&u(this,d,y).call(this,m)?m.__staleWhileFetching:m;x!==void 0&&(l.oldValue=x)}}else l&&(l.set="update")}if(o===0||i(this,N)||u(this,d,Qt).call(this),i(this,N)&&(g||i(this,Et).call(this,p,o,n),l&&i(this,Q).call(this,l,p)),!a&&i(this,R)&&i(this,I)){const m=i(this,I);let x;for(;x=m==null?void 0:m.shift();)(F=i(this,B))==null||F.call(this,...x)}return this}pop(){var t;try{for(;i(this,T);){const e=i(this,_)[i(this,U)];if(u(this,d,Ht).call(this,!0),u(this,d,y).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(i(this,R)&&i(this,I)){const e=i(this,I);let s;for(;s=e==null?void 0:e.shift();)(t=i(this,B))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:o}=e,n=i(this,M).get(t);if(n!==void 0){const a=i(this,_)[n];if(u(this,d,y).call(this,a)&&a.__staleWhileFetching===void 0)return!1;if(!i(this,W).call(this,n))return s&&i(this,ft).call(this,n),o&&(o.has="hit",i(this,Q).call(this,o,n)),!0;o&&(o.has="stale",i(this,Q).call(this,o,n))}else o&&(o.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,o=i(this,M).get(t);if(o===void 0||!s&&i(this,W).call(this,o))return;const n=i(this,_)[o];return u(this,d,y).call(this,n)?n.__staleWhileFetching:n}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:o=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:a=this.ttl,noDisposeOnSet:h=this.noDisposeOnSet,size:l=0,sizeCalculation:g=this.sizeCalculation,noUpdateTTL:b=this.noUpdateTTL,noDeleteOnFetchRejection:p=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:O=this.allowStaleOnFetchRejection,ignoreFetchAbort:w=this.ignoreFetchAbort,allowStaleOnFetchAbort:z=this.allowStaleOnFetchAbort,context:k,forceRefresh:F=!1,status:m,signal:x}=e;if(!i(this,rt))return m&&(m.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:o,noDeleteOnStaleGet:n,status:m});const Lt={allowStale:s,updateAgeOnGet:o,noDeleteOnStaleGet:n,ttl:a,noDisposeOnSet:h,size:l,sizeCalculation:g,noUpdateTTL:b,noDeleteOnFetchRejection:p,allowStaleOnFetchRejection:O,allowStaleOnFetchAbort:z,ignoreFetchAbort:w,status:m,signal:x};let X=i(this,M).get(t);if(X===void 0){m&&(m.fetch="miss");const V=u(this,d,Nt).call(this,t,X,Lt,k);return V.__returned=V}{const V=i(this,_)[X];if(u(this,d,y).call(this,V)){const Ut=s&&V.__staleWhileFetching!==void 0;return m&&(m.fetch="inflight",Ut&&(m.returnedStale=!0)),Ut?V.__staleWhileFetching:V.__returned=V}const Tt=i(this,W).call(this,X);if(!F&&!Tt)return m&&(m.fetch="hit"),u(this,d,Ft).call(this,X),o&&i(this,ft).call(this,X),m&&i(this,Q).call(this,m,X),V;const st=u(this,d,Nt).call(this,t,X,Lt,k),mt=st.__staleWhileFetching!==void 0&&s;return m&&(m.fetch=Tt?"stale":"refresh",mt&&Tt&&(m.returnedStale=!0)),mt?st.__staleWhileFetching:st.__returned=st}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=i(this,bt);if(!s)throw new Error("no memoMethod provided to constructor");const{context:o,forceRefresh:n,...a}=e,h=this.get(t,a);if(!n&&h!==void 0)return h;const l=s(t,h,{options:a,context:o});return this.set(t,l,a),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:o=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:a}=e,h=i(this,M).get(t);if(h!==void 0){const l=i(this,_)[h],g=u(this,d,y).call(this,l);return a&&i(this,Q).call(this,a,h),i(this,W).call(this,h)?(a&&(a.get="stale"),g?(a&&s&&l.__staleWhileFetching!==void 0&&(a.returnedStale=!0),s?l.__staleWhileFetching:void 0):(n||u(this,d,at).call(this,t,"expire"),a&&s&&(a.returnedStale=!0),s?l:void 0)):(a&&(a.get="hit"),g?l.__staleWhileFetching:(u(this,d,Ft).call(this,h),o&&i(this,ft).call(this,h),l))}a&&(a.get="miss")}delete(t){return u(this,d,at).call(this,t,"delete")}clear(){return u(this,d,se).call(this,"delete")}};j=new WeakMap,G=new WeakMap,q=new WeakMap,B=new WeakMap,Ct=new WeakMap,bt=new WeakMap,T=new WeakMap,$=new WeakMap,M=new WeakMap,S=new WeakMap,_=new WeakMap,D=new WeakMap,H=new WeakMap,U=new WeakMap,A=new WeakMap,Z=new WeakMap,I=new WeakMap,K=new WeakMap,J=new WeakMap,N=new WeakMap,Y=new WeakMap,rt=new WeakMap,R=new WeakMap,d=new WeakSet,Qt=function(){const t=new Gt(i(this,j)),e=new Gt(i(this,j));f(this,N,t),f(this,J,e),f(this,Et,(n,a,h=pt.now())=>{if(e[n]=a!==0?h:0,t[n]=a,a!==0&&this.ttlAutopurge){const l=setTimeout(()=>{i(this,W).call(this,n)&&u(this,d,at).call(this,i(this,S)[n],"expire")},a+1);l.unref&&l.unref()}}),f(this,ft,n=>{e[n]=t[n]!==0?pt.now():0}),f(this,Q,(n,a)=>{if(t[a]){const h=t[a],l=e[a];if(!h||!l)return;n.ttl=h,n.start=l,n.now=s||o();const g=n.now-l;n.remainingTTL=h-g}});let s=0;const o=()=>{const n=pt.now();if(this.ttlResolution>0){s=n;const a=setTimeout(()=>s=0,this.ttlResolution);a.unref&&a.unref()}return n};this.getRemainingTTL=n=>{const a=i(this,M).get(n);if(a===void 0)return 0;const h=t[a],l=e[a];return!h||!l?1/0:h-((s||o())-l)},f(this,W,n=>{const a=e[n],h=t[n];return!!h&&!!a&&(s||o())-a>h})},ft=new WeakMap,Q=new WeakMap,Et=new WeakMap,W=new WeakMap,ke=function(){const t=new Gt(i(this,j));f(this,$,0),f(this,K,t),f(this,gt,e=>{f(this,$,i(this,$)-t[e]),t[e]=0}),f(this,Ot,(e,s,o,n)=>{if(u(this,d,y).call(this,s))return 0;if(!it(o)){if(!n)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(o=n(s,e),!it(o))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return o}),f(this,yt,(e,s,o)=>{if(t[e]=s,i(this,G)){const n=i(this,G)-t[e];for(;i(this,$)>n;)u(this,d,Ht).call(this,!0)}f(this,$,i(this,$)+t[e]),o&&(o.entrySize=s,o.totalCalculatedSize=i(this,$))})},gt=new WeakMap,yt=new WeakMap,Ot=new WeakMap,nt=function*({allowStale:t=this.allowStale}={}){if(i(this,T))for(let e=i(this,A);u(this,d,te).call(this,e)&&(!t&&i(this,W).call(this,e)||(yield e),e!==i(this,U));)e=i(this,H)[e]},ot=function*({allowStale:t=this.allowStale}={}){if(i(this,T))for(let e=i(this,U);u(this,d,te).call(this,e)&&(!t&&i(this,W).call(this,e)||(yield e),e!==i(this,A));)e=i(this,D)[e]},te=function(t){return t!==void 0&&i(this,M).get(i(this,S)[t])===t},Ht=function(t){var n,a;const e=i(this,U),s=i(this,S)[e],o=i(this,_)[e];return i(this,rt)&&u(this,d,y).call(this,o)?o.__abortController.abort(new Error("evicted")):(i(this,Y)||i(this,R))&&(i(this,Y)&&((n=i(this,q))==null||n.call(this,o,s,"evict")),i(this,R)&&((a=i(this,I))==null||a.push([o,s,"evict"]))),i(this,gt).call(this,e),t&&(i(this,S)[e]=void 0,i(this,_)[e]=void 0,i(this,Z).push(e)),i(this,T)===1?(f(this,U,f(this,A,0)),i(this,Z).length=0):f(this,U,i(this,D)[e]),i(this,M).delete(s),Dt(this,T)._--,e},Nt=function(t,e,s,o){const n=e===void 0?void 0:i(this,_)[e];if(u(this,d,y).call(this,n))return n;const a=new jt,{signal:h}=s;h==null||h.addEventListener("abort",()=>a.abort(h.reason),{signal:a.signal});const l={signal:a.signal,options:s,context:o},g=(w,z=!1)=>{const{aborted:k}=a.signal,F=s.ignoreFetchAbort&&w!==void 0;if(s.status&&(k&&!z?(s.status.fetchAborted=!0,s.status.fetchError=a.signal.reason,F&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),k&&!F&&!z)return b(a.signal.reason);const m=p;return i(this,_)[e]===p&&(w===void 0?m.__staleWhileFetching?i(this,_)[e]=m.__staleWhileFetching:u(this,d,at).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,w,l.options))),w},b=w=>{const{aborted:z}=a.signal,k=z&&s.allowStaleOnFetchAbort,F=k||s.allowStaleOnFetchRejection,m=F||s.noDeleteOnFetchRejection,x=p;if(i(this,_)[e]===p&&(!m||x.__staleWhileFetching===void 0?u(this,d,at).call(this,t,"fetch"):k||(i(this,_)[e]=x.__staleWhileFetching)),F)return s.status&&x.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),x.__staleWhileFetching;if(x.__returned===x)throw w};s.status&&(s.status.fetchDispatched=!0);const p=new Promise((w,z)=>{var F;const k=(F=i(this,Ct))==null?void 0:F.call(this,t,n,l);k&&k instanceof Promise&&k.then(m=>w(m===void 0?void 0:m),z),a.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(w(void 0),s.allowStaleOnFetchAbort&&(w=m=>g(m,!0)))})}).then(g,w=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=w),b(w))),O=Object.assign(p,{__abortController:a,__staleWhileFetching:n,__returned:void 0});return e===void 0?(this.set(t,O,{...l.options,status:void 0}),e=i(this,M).get(t)):i(this,_)[e]=O,O},y=function(t){if(!i(this,rt))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof jt},ee=function(t,e){i(this,H)[e]=t,i(this,D)[t]=e},Ft=function(t){t!==i(this,A)&&(t===i(this,U)?f(this,U,i(this,D)[t]):u(this,d,ee).call(this,i(this,H)[t],i(this,D)[t]),u(this,d,ee).call(this,i(this,A),t),f(this,A,t))},at=function(t,e){var o,n,a,h;let s=!1;if(i(this,T)!==0){const l=i(this,M).get(t);if(l!==void 0)if(s=!0,i(this,T)===1)u(this,d,se).call(this,e);else{i(this,gt).call(this,l);const g=i(this,_)[l];if(u(this,d,y).call(this,g)?g.__abortController.abort(new Error("deleted")):(i(this,Y)||i(this,R))&&(i(this,Y)&&((o=i(this,q))==null||o.call(this,g,t,e)),i(this,R)&&((n=i(this,I))==null||n.push([g,t,e]))),i(this,M).delete(t),i(this,S)[l]=void 0,i(this,_)[l]=void 0,l===i(this,A))f(this,A,i(this,H)[l]);else if(l===i(this,U))f(this,U,i(this,D)[l]);else{const b=i(this,H)[l];i(this,D)[b]=i(this,D)[l];const p=i(this,D)[l];i(this,H)[p]=i(this,H)[l]}Dt(this,T)._--,i(this,Z).push(l)}}if(i(this,R)&&((a=i(this,I))!=null&&a.length)){const l=i(this,I);let g;for(;g=l==null?void 0:l.shift();)(h=i(this,B))==null||h.call(this,...g)}return s},se=function(t){var e,s,o;for(const n of u(this,d,ot).call(this,{allowStale:!0})){const a=i(this,_)[n];if(u(this,d,y).call(this,a))a.__abortController.abort(new Error("deleted"));else{const h=i(this,S)[n];i(this,Y)&&((e=i(this,q))==null||e.call(this,a,h,t)),i(this,R)&&((s=i(this,I))==null||s.push([a,h,t]))}}if(i(this,M).clear(),i(this,_).fill(void 0),i(this,S).fill(void 0),i(this,N)&&i(this,J)&&(i(this,N).fill(0),i(this,J).fill(0)),i(this,K)&&i(this,K).fill(0),f(this,U,0),f(this,A,0),i(this,Z).length=0,f(this,$,0),f(this,T,0),i(this,R)&&i(this,I)){const n=i(this,I);let a;for(;a=n==null?void 0:n.shift();)(o=i(this,B))==null||o.call(this,...a)}};let Xt=ie;class ks{constructor(){r(this,"_syncStatus",{status:$e.done,foldersProgress:[]});r(this,"_syncEnabledState",he.initializing);r(this,"_workspaceGuidelines",[]);r(this,"_openUserGuidelinesInput",!1);r(this,"_userGuidelines");r(this,"_contextStore",new vs);r(this,"_prevOpenFiles",[]);r(this,"_disableContext",!1);r(this,"_enableAgentMemories",!1);r(this,"subscribers",new Set);r(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));r(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case dt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case dt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case dt.fileRangesSelected:this.updateSelections(e.data);break;case dt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case dt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case dt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});r(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:E.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});r(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});r(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});r(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});r(this,"addFile",t=>{this.addFiles([t])});r(this,"addFiles",t=>{this.updateFiles(t,[])});r(this,"removeFile",t=>{this.removeFiles([t])});r(this,"removeFiles",t=>{this.updateFiles([],t)});r(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});r(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});r(this,"updateFiles",(t,e)=>{const s=a=>({file:a,...Zt(a)}),o=t.map(s),n=e.map(s);this._contextStore.update(o,n,a=>a.id),this.notifySubscribers()});r(this,"updateRules",(t,e)=>{const s=a=>({rule:a,...Qe(a)}),o=t.map(s),n=e.map(s);this._contextStore.update(o,n,a=>a.id),this.notifySubscribers()});r(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});r(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});r(this,"setCurrentlyOpenFiles",t=>{const e=t.map(o=>({recentFile:o,...Zt(o)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,o=>o.id),s.forEach(o=>{const n=this._contextStore.peekKey(o.id);n!=null&&n.recentFile&&(n.file=n.recentFile,delete n.recentFile)}),e.forEach(o=>{const n=this._contextStore.peekKey(o.id);n!=null&&n.file&&(n.recentFile=n.file,delete n.file)}),this.notifySubscribers()});r(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});r(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,o=t.overLimit||((e==null?void 0:e.overLimit)??!1),n={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:E.active,referenceCount:1,showWarning:o,rulesAndGuidelinesState:e};this._contextStore.update([n],s,a=>{var h;return a.id+String((h=a.userGuidelines)==null?void 0:h.overLimit)}),this.notifySubscribers()});r(this,"onGuidelinesStateUpdate",t=>{var o;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const n=e||{overLimit:!1,contents:"",lengthLimit:((o=t.rulesAndGuidelines)==null?void 0:o.lengthLimit)??2e3};this.updateUserGuidelines(n,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(n=>n.sourceFolder))});r(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(o=>o.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));r(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});r(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});r(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});r(this,"updateSelections",t=>{const e=this._contextStore.values.filter(ce),s=t.map(o=>({selection:o,...Zt(o)}));this._contextStore.update([],e,o=>o.id),this._contextStore.update(s,[],o=>o.id),this.notifySubscribers()});r(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});r(this,"markInactive",t=>{this.markItemsInactive([t])});r(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,E.inactive)}),this.notifySubscribers()});r(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});r(this,"markActive",t=>{this.markItemsActive([t])});r(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,E.active)}),this.notifySubscribers()});r(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});r(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});r(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});r(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Ze(t)&&!le(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(le)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(ce)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Ke)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(de)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Je)}get userGuidelines(){return this._contextStore.values.filter(ue)}get agentMemories(){return[{...Ye,status:this._enableAgentMemories?E.active:E.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>fe(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===E.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===E.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===E.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===E.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===E.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===E.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===he.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(g=>g.progress!==void 0);if(t.length===0)return;const e=t.reduce((g,b)=>{var p;return g+(((p=b==null?void 0:b.progress)==null?void 0:p.trackedFiles)??0)},0),s=t.reduce((g,b)=>{var p;return g+(((p=b==null?void 0:b.progress)==null?void 0:p.backlogSize)??0)},0),o=Math.max(e,0),n=Math.min(Math.max(s,0),o),a=o-n,h=[];for(const g of t)(l=g==null?void 0:g.progress)!=null&&l.newlyTracked&&h.push(g.folderRoot);return{status:this._syncStatus.status,totalFiles:o,syncedCount:a,backlogSize:n,newlyTrackedFolders:h}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(de(t)||ue(t)||Xe(t)||fe(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===E.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===E.inactive)}get isContextDisabled(){return this._disableContext}}class vs{constructor(){r(this,"_cache",new Xt({max:1e3}));r(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));r(this,"clear",()=>{this._cache.clear()});r(this,"update",(t,e,s)=>{t.forEach(o=>this.addInPlace(o,s)),e.forEach(o=>this.removeInPlace(o,s))});r(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});r(this,"addInPlace",(t,e)=>{const s=e(t),o=t.referenceCount??1,n=this._cache.get(s),a=t.status??(n==null?void 0:n.status)??E.active;n?(n.referenceCount+=o,n.status=a,n.pinned=t.pinned??n.pinned,n.showWarning=t.showWarning??n.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in n&&(n.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in n&&(n.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:o,status:a})});r(this,"removeInPlace",(t,e)=>{const s=e(t),o=this._cache.get(s);o&&(o.referenceCount-=1,o.referenceCount===0&&this._cache.delete(s))});r(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});r(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});r(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});r(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});r(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===E.active?E.inactive:E.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}function pe(c,t,e){const s=c.slice();return s[3]=t[e],s}function ve(c){let t,e,s,o=c[3]+"";return{c(){t=Ae("span"),e=Ge(o),s=He(),C(t,"class","c-keyboard-shortcut-hint__icon svelte-1txw16l")},m(n,a){kt(n,t,a),It(t,e),It(t,s)},p(n,a){2&a&&o!==(o=n[3]+"")&&ze(e,o)},d(n){n&&et(t)}}}function Cs(c){let t,e,s=oe(c[1]),o=[];for(let n=0;n<s.length;n+=1)o[n]=ve(pe(c,s,n));return{c(){t=Ae("span");for(let n=0;n<o.length;n+=1)o[n].c();C(t,"class",e=ae(`c-keyboard-shortcut-hint ${c[0]}`)+" svelte-1txw16l")},m(n,a){kt(n,t,a);for(let h=0;h<o.length;h+=1)o[h]&&o[h].m(t,null)},p(n,[a]){if(2&a){let h;for(s=oe(n[1]),h=0;h<s.length;h+=1){const l=pe(n,s,h);o[h]?o[h].p(l,a):(o[h]=ve(l),o[h].c(),o[h].m(t,null))}for(;h<o.length;h+=1)o[h].d(1);o.length=s.length}1&a&&e!==(e=ae(`c-keyboard-shortcut-hint ${n[0]}`)+" svelte-1txw16l")&&C(t,"class",e)},i:P,o:P,d(n){n&&et(t),Pe(o,n)}}}function bs(c,t,e){let{class:s=""}=t,{keybinding:o}=t,{icons:n=(o==null?void 0:o.split("-"))??[]}=t;return c.$$set=a=>{"class"in a&&e(0,s=a.class),"keybinding"in a&&e(2,o=a.keybinding),"icons"in a&&e(1,n=a.icons)},[s,n,o]}class Ls extends wt{constructor(t){super(),xt(this,t,bs,Cs,Mt,{class:0,keybinding:2,icons:1})}}function ys(c){let t,e;return{c(){t=ht("svg"),e=ht("path"),C(e,"fill-rule","evenodd"),C(e,"clip-rule","evenodd"),C(e,"d","M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z"),C(e,"fill","currentColor"),C(t,"width","15"),C(t,"height","15"),C(t,"viewBox","0 0 15 15"),C(t,"fill","none"),C(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){kt(s,t,o),It(t,e)},p:P,i:P,o:P,d(s){s&&et(t)}}}class Us extends wt{constructor(t){super(),xt(this,t,null,ys,Mt,{})}}function Ss(c){let t,e;return{c(){t=ht("svg"),e=ht("path"),C(e,"fill-rule","evenodd"),C(e,"clip-rule","evenodd"),C(e,"d","M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z"),C(e,"fill","currentColor"),C(t,"width","16"),C(t,"height","16"),C(t,"viewBox","0 0 16 16"),C(t,"fill","none"),C(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){kt(s,t,o),It(t,e)},p:P,i:P,o:P,d(s){s&&et(t)}}}class Ds extends wt{constructor(t){super(),xt(this,t,null,Ss,Mt,{})}}function ws(c){let t,e;return{c(){t=ht("svg"),e=ht("path"),C(e,"d","M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z"),C(e,"fill","currentColor"),C(t,"width","16"),C(t,"height","16"),C(t,"viewBox","0 0 16 16"),C(t,"fill","none"),C(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){kt(s,t,o),It(t,e)},p:P,i:P,o:P,d(s){s&&et(t)}}}class Rs extends wt{constructor(t){super(),xt(this,t,null,ws,Mt,{})}}export{_s as A,ps as C,Os as D,Ds as F,Ls as K,ks as S,Us as a,Rs as b,zt as c,cs as d,ls as e};

syntax = "proto3";

package com.augmentcode.common.webviews.protos;

import "google/protobuf/descriptor.proto";

option java_multiple_files = true;
option java_outer_classname = "WebViewMsgDefTypes";
option java_package = "com.augmentcode.common.webviews.protos";

extend google.protobuf.MessageOptions {
  // string value type used in JSONs coming to us
  // see WebViewMessageType enum in webview-messages.ts
  string webview_message_type = 239238; // A unique field number for us
}

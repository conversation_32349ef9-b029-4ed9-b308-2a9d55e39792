import{S,i as A,s as K,W as L,$ as x,a as v,d,D as M,t as $,q as u,a0 as y,g as N,a1 as p,c as f,E as V,F as g,G as W,a2 as b,I as k,j as B,J as D,o as H,p as O,K as E,L as F,M as G,V as P,h as I}from"./SpinnerAugment-BejxPZX4.js";const Q=o=>({}),j=o=>({});function w(o){let a,c;const i=o[8].icon,l=D(i,o,o[9],j);return{c(){a=g("div"),l&&l.c(),I(a,"class","c-callout-icon svelte-1u5qnh6")},m(e,n){f(e,a,n),l&&l.m(a,null),c=!0},p(e,n){l&&l.p&&(!c||512&n)&&E(l,i,e,e[9],c?G(i,e[9],n,Q):F(e[9]),j)},i(e){c||(u(l,e),c=!0)},o(e){$(l,e),c=!1},d(e){e&&d(a),l&&l.d(e)}}}function R(o){let a,c,i,l=o[7].icon&&w(o);const e=o[8].default,n=D(e,o,o[9],null);return{c(){l&&l.c(),a=P(),c=g("div"),n&&n.c(),I(c,"class","c-callout-body svelte-1u5qnh6")},m(t,s){l&&l.m(t,s),f(t,a,s),f(t,c,s),n&&n.m(c,null),i=!0},p(t,s){t[7].icon?l?(l.p(t,s),128&s&&u(l,1)):(l=w(t),l.c(),u(l,1),l.m(a.parentNode,a)):l&&(H(),$(l,1,1,()=>{l=null}),O()),n&&n.p&&(!i||512&s)&&E(n,e,t,t[9],i?G(e,t[9],s,null):F(t[9]),null)},i(t){i||(u(l),u(n,t),i=!0)},o(t){$(l),$(n,t),i=!1},d(t){t&&(d(a),d(c)),l&&l.d(t),n&&n.d(t)}}}function T(o){let a,c,i,l;c=new L({props:{size:o[6],$$slots:{default:[R]},$$scope:{ctx:o}}});let e=[x(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=v(n,e[t]);return{c(){a=g("div"),W(c.$$.fragment),y(a,n),p(a,"c-callout--highContrast",o[3]),p(a,"svelte-1u5qnh6",!0)},m(t,s){f(t,a,s),V(c,a,null),l=!0},p(t,[s]){const h={};640&s&&(h.$$scope={dirty:s,ctx:t}),c.$set(h),y(a,n=N(e,[1&s&&x(t[0]),(!l||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),p(a,"c-callout--highContrast",t[3]),p(a,"svelte-1u5qnh6",!0)},i(t){l||(u(c.$$.fragment,t),l=!0)},o(t){$(c.$$.fragment,t),l=!1},d(t){t&&d(a),M(c)}}}function U(o,a,c){let i,l;const e=["color","variant","size","highContrast"];let n=b(a,e),{$$slots:t={},$$scope:s}=a;const h=k(t);let{color:z="info"}=a,{variant:C="soft"}=a,{size:m=2}=a,{highContrast:q=!1}=a;const J=m;return o.$$set=r=>{a=v(v({},a),B(r)),c(10,n=b(a,e)),"color"in r&&c(0,z=r.color),"variant"in r&&c(1,C=r.variant),"size"in r&&c(2,m=r.size),"highContrast"in r&&c(3,q=r.highContrast),"$$scope"in r&&c(9,s=r.$$scope)},o.$$.update=()=>{c(5,{class:i,...l}=n,i,(c(4,l),c(10,n)))},[z,C,m,q,l,i,J,h,t,s]}class Y extends S{constructor(a){super(),A(this,a,U,T,K,{color:0,variant:1,size:2,highContrast:3})}}export{Y as C};

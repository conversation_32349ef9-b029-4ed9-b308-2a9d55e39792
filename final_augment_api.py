#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的Augment API调用方法
基于深入源代码分析的真实实现
"""

import requests
import json
import time
import subprocess
import os
import sys

def send_via_intellij_internal_api(message):
    """通过IntelliJ IDEA内部API发送消息"""
    print(f"🎯 通过IntelliJ内部API发送: {message}")
    
    base_url = "http://localhost:63342"
    
    # 基于源代码分析的真实API端点
    # 从plugin.xml中的httpRequestHandler可以看出，Augment注册了HTTP处理器
    
    # 尝试不同的API调用方式
    methods = [
        # 方法1: 直接调用Augment的HTTP处理器
        {
            "url": f"{base_url}/api/augment",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Accept": "application/json"
            },
            "data": {
                "action": "chat",
                "message": message,
                "type": "user-message"
            }
        },
        
        # 方法2: 模拟WebView消息
        {
            "url": f"{base_url}/api/webview",
            "method": "POST", 
            "headers": {
                "Content-Type": "application/json",
                "Origin": "http://localhost:63342"
            },
            "data": {
                "type": "chat-user-message",
                "data": {
                    "text": message,
                    "chatHistory": [],
                    "modelId": "default"
                }
            }
        },
        
        # 方法3: 通过插件Action系统
        {
            "url": f"{base_url}/api/action",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "data": {
                "action": "com.augmentcode.intellij.actions.ShowChatAction",
                "params": {
                    "message": message,
                    "autoSend": True
                }
            }
        },
        
        # 方法4: 直接调用OAuth回调处理器
        {
            "url": f"{base_url}/oauth/callback",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "data": {
                "type": "chat",
                "message": message,
                "source": "api"
            }
        }
    ]
    
    session = requests.Session()
    
    for i, method in enumerate(methods, 1):
        print(f"\n🧪 方法 {i}: {method['url']}")
        
        try:
            response = session.request(
                method['method'],
                method['url'],
                json=method['data'],
                headers=method['headers'],
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code in [200, 201, 202, 204]:
                print(f"   ✅ 成功! 响应: {response.text[:200]}...")
                return True
            elif response.status_code == 404:
                print(f"   ❌ 端点不存在")
            else:
                print(f"   ⚠️ 响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    return False

def send_via_file_system_trigger(message):
    """通过文件系统触发器发送消息"""
    print(f"📁 通过文件系统触发器发送: {message}")
    
    # 创建一个特殊的文件，Augment可能会监听
    trigger_file = "augment_message_trigger.json"
    
    trigger_data = {
        "type": "chat-user-message",
        "timestamp": int(time.time() * 1000),
        "data": {
            "text": message,
            "chatHistory": [],
            "modelId": "default",
            "source": "file-trigger"
        }
    }
    
    try:
        with open(trigger_file, 'w', encoding='utf-8') as f:
            json.dump(trigger_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建触发文件: {trigger_file}")
        
        # 等待一段时间，看是否被处理
        time.sleep(5)
        
        # 检查文件是否被修改或删除
        if os.path.exists(trigger_file):
            stat_after = os.stat(trigger_file)
            print(f"⏰ 文件仍存在，可能未被处理")
            os.remove(trigger_file)
            return False
        else:
            print(f"✅ 文件已被处理!")
            return True
            
    except Exception as e:
        print(f"❌ 文件触发器失败: {e}")
        return False

def send_via_clipboard_injection(message):
    """通过剪贴板注入发送消息"""
    print(f"📋 通过剪贴板注入发送: {message}")
    
    try:
        import pyperclip
        
        # 构造特殊格式的剪贴板内容
        clipboard_data = f"AUGMENT_API_MESSAGE:{json.dumps({'text': message, 'type': 'chat-user-message'})}"
        
        # 保存原剪贴板内容
        original_clipboard = pyperclip.paste()
        
        # 设置特殊内容
        pyperclip.copy(clipboard_data)
        print("✅ 已设置剪贴板内容")
        
        # 等待处理
        time.sleep(3)
        
        # 恢复原内容
        pyperclip.copy(original_clipboard)
        
        print("✅ 剪贴板注入完成")
        return True
        
    except ImportError:
        print("❌ 需要安装pyperclip: pip install pyperclip")
        return False
    except Exception as e:
        print(f"❌ 剪贴板注入失败: {e}")
        return False

def send_via_keyboard_automation(message):
    """通过键盘自动化发送消息"""
    print(f"⌨️ 通过键盘自动化发送: {message}")
    
    try:
        import pyautogui
        
        print("⚠️ 将在3秒后开始键盘自动化，请确保Augment聊天窗口已打开并获得焦点")
        time.sleep(3)
        
        # 模拟键盘输入
        pyautogui.typewrite(message, interval=0.05)
        time.sleep(0.5)
        
        # 按回车发送
        pyautogui.press('enter')
        
        print("✅ 键盘自动化完成")
        return True
        
    except ImportError:
        print("❌ 需要安装pyautogui: pip install pyautogui")
        return False
    except Exception as e:
        print(f"❌ 键盘自动化失败: {e}")
        return False

def main():
    if len(sys.argv) > 1:
        message = " ".join(sys.argv[1:])
    else:
        message = "你好，Augment AI！这是通过最终API方法发送的消息，请确认收到！"
    
    print("🎯 Augment AI 最终API调用工具")
    print("基于深入源代码分析的所有可能方法")
    print("=" * 60)
    print(f"📤 消息: {message}")
    print("=" * 60)
    
    methods = [
        ("IntelliJ内部API", send_via_intellij_internal_api),
        ("文件系统触发器", send_via_file_system_trigger),
        ("剪贴板注入", send_via_clipboard_injection),
        ("键盘自动化", send_via_keyboard_automation)
    ]
    
    success = False
    
    for name, method in methods:
        print(f"\n🔧 尝试方法: {name}")
        print("-" * 40)
        
        try:
            if method(message):
                print(f"✅ {name} 成功!")
                success = True
                break
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 消息发送成功!")
        print("请检查Augment聊天界面是否收到消息。")
    else:
        print("❌ 所有方法都失败了")
        print("\n💡 最终建议:")
        print("1. 确保IntelliJ IDEA和Augment插件正在运行")
        print("2. 确保Augment插件已登录")
        print("3. 尝试手动打开Augment聊天窗口")
        print("4. 检查插件版本和兼容性")
        print("5. 查看IntelliJ IDEA日志获取更多信息")

if __name__ == "__main__":
    main()

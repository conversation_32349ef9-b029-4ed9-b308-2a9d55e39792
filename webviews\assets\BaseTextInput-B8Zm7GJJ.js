import{S as x,i as m,s as v,W as b,a as g,d as y,D as z,t as d,q as f,a0 as u,g as h,a1 as p,c as q,E as j,F as w,G as B,$ as D,J as E,K as F,L as G,M as J}from"./SpinnerAugment-BejxPZX4.js";import"./IconButtonAugment-DmfIVAYG.js";function K(i){let s;const n=i[5].default,t=E(n,i,i[6],null);return{c(){t&&t.c()},m(a,c){t&&t.m(a,c),s=!0},p(a,c){t&&t.p&&(!s||64&c)&&F(t,n,a,a[6],s?J(n,a[6],c,null):G(a[6]),null)},i(a){s||(f(t,a),s=!0)},o(a){d(t,a),s=!1},d(a){t&&t.d(a)}}}function L(i){let s,n,t,a;n=new b({props:{type:i[2],size:i[1],$$slots:{default:[K]},$$scope:{ctx:i}}});let c=[i[4],{class:t=`c-base-text-input c-base-text-input--${i[0]} c-base-text-input--size-${i[1]}`}],l={};for(let e=0;e<c.length;e+=1)l=g(l,c[e]);return{c(){s=w("div"),B(n.$$.fragment),u(s,l),p(s,"c-base-text-input--has-color",i[3]!==void 0),p(s,"svelte-126qjdg",!0)},m(e,o){q(e,s,o),j(n,s,null),a=!0},p(e,[o]){const r={};4&o&&(r.type=e[2]),2&o&&(r.size=e[1]),64&o&&(r.$$scope={dirty:o,ctx:e}),n.$set(r),u(s,l=h(c,[16&o&&e[4],(!a||3&o&&t!==(t=`c-base-text-input c-base-text-input--${e[0]} c-base-text-input--size-${e[1]}`))&&{class:t}])),p(s,"c-base-text-input--has-color",e[3]!==void 0),p(s,"svelte-126qjdg",!0)},i(e){a||(f(n.$$.fragment,e),a=!0)},o(e){d(n.$$.fragment,e),a=!1},d(e){e&&y(s),z(n)}}}function M(i,s,n){let t,{$$slots:a={},$$scope:c}=s,{variant:l="surface"}=s,{size:e=2}=s,{type:o="default"}=s,{color:r}=s;return i.$$set=$=>{"variant"in $&&n(0,l=$.variant),"size"in $&&n(1,e=$.size),"type"in $&&n(2,o=$.type),"color"in $&&n(3,r=$.color),"$$scope"in $&&n(6,c=$.$$scope)},i.$$.update=()=>{8&i.$$.dirty&&n(4,t=D(r||"accent"))},[l,e,o,r,t,a,c]}class k extends x{constructor(s){super(),m(this,s,M,L,v,{variant:0,size:1,type:2,color:3})}}export{k as B};

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <script type="module" crossorigin src="./assets/preference-BmN8Ecqx.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BejxPZX4.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DmfIVAYG.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-DH8_XiUe.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-dQY7gB6x.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-D7f6isRQ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-B_Xbv83k.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-B4wu8Zer.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-B8Zm7GJJ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-LNVO_dOZ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-ChvTYBfK.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BlaqVU85.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-6AlVWKh5.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/SimpleMonaco-DSk2r097.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-BLNMYUZC.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-Dt1rTGTc.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/chat-context-CmoAn9pE.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DwCOg82s.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-DHtiwZCr.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CVatBELe.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DCLSWWW0.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DltEK_xL.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-03DKN6zM.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-PxcJW8dJ.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-CUEAtZVx.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/copy-BA1J_YQn.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-Cb58npax.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BRv5sK55.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-B9tL-FlO.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-BUMTFdYT.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-DMucGkIH.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/index-Dkoz80k5.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-BRK9m6wM.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-5PNRCAQu.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-D78W1-g3.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/check-Cc9E51ln.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-bvgL2GJ0.js" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-VPe0cp57.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-FeoFGSYm.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-cBJ5l_kp.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-hTsrGIsd.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-pxiddGnV.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-B23ZKhTC.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/index-CPuOq1ei.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BzgVnrJx.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-DTcQ2vsq.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-Bx7Exmp0.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-DFdffRNP.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/partner-mcp-utils-BRhZ8F7l.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMsGeQ5J.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
    <link rel="stylesheet" crossorigin href="./assets/preference-6d_5zsjM.css" nonce="nonce-K4FJjxCvPkSyPDZ1lzPCeA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>

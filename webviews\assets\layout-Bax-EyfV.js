import{G as R}from"./graph-KsABUsDv.js";import{b as Yn,p as yn,q as xn,g as rn,e as cn,l as J,o as zn,s as An,c as Dn,u as Jn,d as h,i as I,f as G,v as j,r as A}from"./_baseUniq-Dec7a2Hj.js";import{f as C,b as kn,a as Qn,c as Hn,d as Kn,t as Q,m as k,e as F,h as En,g as tn,l as Y,i as Un}from"./_basePickBy-DktbUoK0.js";import{b4 as Wn,b5 as Xn,b6 as Zn,aM as $n,b7 as nr,aQ as Nn,aP as On,b8 as rr,aL as Z,aq as tr,aS as er,as as or,b9 as $}from"./AugmentMessage-bvgL2GJ0.js";var ir=1,ar=4;function ur(n,t){return n>t}function B(n,t){var e={};return t=rn(t),xn(n,function(c,o,u){nr(e,o,t(c,o,u))}),e}function T(n){return n&&n.length?kn(n,Nn,ur):void 0}function en(n,t){return n&&n.length?kn(n,rn(t),Qn):void 0}function dr(n,t){if(n!==t){var e=n!==void 0,c=n===null,o=n==n,u=cn(n),r=t!==void 0,a=t===null,i=t==t,d=cn(t);if(!a&&!d&&!u&&n>t||u&&r&&i&&!a&&!d||c&&r&&i||!e&&i||!o)return 1;if(!c&&!u&&!d&&n<t||d&&e&&o&&!c&&!u||a&&e&&o||!r&&o||!i)return-1}return 0}function cr(n,t,e){t=t.length?J(t,function(o){return On(o)?function(u){return zn(u,o.length===1?o[0]:o)}:o}):[Nn];var c=-1;return t=J(t,rr(rn)),function(o,u){var r=o.length;for(o.sort(u);r--;)o[r]=o[r].value;return o}(Hn(n,function(o,u,r){return{criteria:J(t,function(a){return a(o)}),index:++c,value:o}}),function(o,u){return function(r,a,i){for(var d=-1,f=r.criteria,s=a.criteria,g=f.length,v=i.length;++d<g;){var l=dr(f[d],s[d]);if(l)return d>=v?l:l*(i[d]=="desc"?-1:1)}return r.index-a.index}(o,u,e)})}var sn,z=Wn(Xn(sn=function(n,t){return n==null?{}:function(e,c){return Kn(e,c,function(o,u){return An(e,u)})}(n,t)},void 0,C),sn+""),sr=Math.ceil,fr=Math.max,L=function(n,t,e){return e&&typeof e!="number"&&Z(n,t,e)&&(t=e=void 0),n=Q(n),t===void 0?(t=n,n=0):t=Q(t),function(c,o,u,r){for(var a=-1,i=fr(sr((o-c)/(u||1)),0),d=Array(i);i--;)d[++a]=c,c+=u;return d}(n,t,e=e===void 0?n<t?1:-1:Q(e))},V=tr(function(n,t){if(n==null)return[];var e=t.length;return e>1&&Z(n,t[0],t[1])?t=[]:e>2&&Z(t[0],t[1],t[2])&&(t=[t[0]]),cr(n,Dn(t),[])}),hr=0;function on(n){var t=++hr;return Jn(n)+t}class gr{constructor(){var t={};t._next=t._prev=t,this._sentinel=t}dequeue(){var t=this._sentinel,e=t._prev;if(e!==t)return fn(e),e}enqueue(t){var e=this._sentinel;t._prev&&t._next&&fn(t),t._next=e._next,e._next._prev=t,e._next=t,t._prev=e}toString(){for(var t=[],e=this._sentinel,c=e._prev;c!==e;)t.push(JSON.stringify(c,vr)),c=c._prev;return"["+t.join(", ")+"]"}}function fn(n){n._prev._next=n._next,n._next._prev=n._prev,delete n._next,delete n._prev}function vr(n,t){if(n!=="_next"&&n!=="_prev")return t}var lr=or(1);function pr(n,t){if(n.nodeCount()<=1)return[];var e=function(o,u){var r=new R,a=0,i=0;h(o.nodes(),function(s){r.setNode(s,{v:s,in:0,out:0})}),h(o.edges(),function(s){var g=r.edge(s.v,s.w)||0,v=u(s),l=g+v;r.setEdge(s.v,s.w,l),i=Math.max(i,r.node(s.v).out+=v),a=Math.max(a,r.node(s.w).in+=v)});var d=L(i+a+3).map(function(){return new gr}),f=a+1;return h(r.nodes(),function(s){nn(d,f,r.node(s))}),{graph:r,buckets:d,zeroIdx:f}}(n,t||lr),c=function(o,u,r){for(var a,i=[],d=u[u.length-1],f=u[0];o.nodeCount();){for(;a=f.dequeue();)H(o,u,r,a);for(;a=d.dequeue();)H(o,u,r,a);if(o.nodeCount()){for(var s=u.length-2;s>0;--s)if(a=u[s].dequeue()){i=i.concat(H(o,u,r,a,!0));break}}}return i}(e.graph,e.buckets,e.zeroIdx);return C(k(c,function(o){return n.outEdges(o.v,o.w)}))}function H(n,t,e,c,o){var u=o?[]:void 0;return h(n.inEdges(c.v),function(r){var a=n.edge(r),i=n.node(r.v);o&&u.push({v:r.v,w:r.w}),i.out-=a,nn(t,e,i)}),h(n.outEdges(c.v),function(r){var a=n.edge(r),i=r.w,d=n.node(i);d.in-=a,nn(t,e,d)}),n.removeNode(c.v),u}function nn(n,t,e){e.out?e.in?n[e.out-e.in+t].enqueue(e):n[n.length-1].enqueue(e):n[0].enqueue(e)}function wr(n){var t=n.graph().acyclicer==="greedy"?pr(n,function(e){return function(c){return e.edge(c).weight}}(n)):function(e){var c=[],o={},u={};function r(a){Object.prototype.hasOwnProperty.call(u,a)||(u[a]=!0,o[a]=!0,h(e.outEdges(a),function(i){Object.prototype.hasOwnProperty.call(o,i.w)?c.push(i):r(i.w)}),delete o[a])}return h(e.nodes(),r),c}(n);h(t,function(e){var c=n.edge(e);n.removeEdge(e),c.forwardName=e.name,c.reversed=!0,n.setEdge(e.w,e.v,c,on("rev"))})}function S(n,t,e,c){var o;do o=on(c);while(n.hasNode(o));return e.dummy=t,n.setNode(o,e),o}function _n(n){var t=new R({multigraph:n.isMultigraph()}).setGraph(n.graph());return h(n.nodes(),function(e){n.children(e).length||t.setNode(e,n.node(e))}),h(n.edges(),function(e){t.setEdge(e,n.edge(e))}),t}function hn(n,t){var e,c,o=n.x,u=n.y,r=t.x-o,a=t.y-u,i=n.width/2,d=n.height/2;if(!r&&!a)throw new Error("Not possible to find intersection inside of the rectangle");return Math.abs(a)*i>Math.abs(r)*d?(a<0&&(d=-d),e=d*r/a,c=d):(r<0&&(i=-i),e=i,c=i*a/r),{x:o+e,y:u+c}}function D(n){var t=k(L(In(n)+1),function(){return[]});return h(n.nodes(),function(e){var c=n.node(e),o=c.rank;I(o)||(t[o][c.order]=e)}),t}function gn(n,t,e,c){var o={width:0,height:0};return arguments.length>=4&&(o.rank=e,o.order=c),S(n,"border",o,t)}function In(n){return T(k(n.nodes(),function(t){var e=n.node(t).rank;if(!I(e))return e}))}function mr(n,t){return t()}function vn(n,t,e,c,o,u){var r={width:0,height:0,rank:u,borderType:t},a=o[t][u-1],i=S(n,"border",r,e);o[t][u]=i,n.setParent(i,c),a&&n.setEdge(a,i,{weight:1})}function br(n){var t=n.graph().rankdir.toLowerCase();t!=="bt"&&t!=="rl"||function(e){h(e.nodes(),function(c){K(e.node(c))}),h(e.edges(),function(c){var o=e.edge(c);h(o.points,K),Object.prototype.hasOwnProperty.call(o,"y")&&K(o)})}(n),t!=="lr"&&t!=="rl"||(function(e){h(e.nodes(),function(c){U(e.node(c))}),h(e.edges(),function(c){var o=e.edge(c);h(o.points,U),Object.prototype.hasOwnProperty.call(o,"x")&&U(o)})}(n),Pn(n))}function Pn(n){h(n.nodes(),function(t){ln(n.node(t))}),h(n.edges(),function(t){ln(n.edge(t))})}function ln(n){var t=n.width;n.width=n.height,n.height=t}function K(n){n.y=-n.y}function U(n){var t=n.x;n.x=n.y,n.y=t}function yr(n){n.graph().dummyChains=[],h(n.edges(),function(t){(function(e,c){var o=c.v,u=e.node(o).rank,r=c.w,a=e.node(r).rank,i=c.name,d=e.edge(c),f=d.labelRank;if(a!==u+1){e.removeEdge(c);var s,g,v=void 0;for(g=0,++u;u<a;++g,++u)d.points=[],s=S(e,"edge",v={width:0,height:0,edgeLabel:d,edgeObj:c,rank:u},"_d"),u===f&&(v.width=d.width,v.height=d.height,v.dummy="edge-label",v.labelpos=d.labelpos),e.setEdge(o,s,{weight:d.weight},i),g===0&&e.graph().dummyChains.push(s),o=s;e.setEdge(o,r,{weight:d.weight},i)}})(n,t)})}function an(n){var t={};h(n.sources(),function e(c){var o=n.node(c);if(Object.prototype.hasOwnProperty.call(t,c))return o.rank;t[c]=!0;var u=F(k(n.outEdges(c),function(r){return e(r.w)-n.edge(r).minlen}));return u!==Number.POSITIVE_INFINITY&&u!=null||(u=0),o.rank=u})}function q(n,t){return n.node(t.w).rank-n.node(t.v).rank-n.edge(t).minlen}function Rn(n){var t,e,c=new R({directed:!1}),o=n.nodes()[0],u=n.nodeCount();for(c.setNode(o,{});xr(c,n)<u;)t=kr(c,n),e=c.hasNode(t.v)?q(n,t):-q(n,t),Er(c,n,e);return c}function xr(n,t){return h(n.nodes(),function e(c){h(t.nodeEdges(c),function(o){var u=o.v,r=c===u?o.w:u;n.hasNode(r)||q(t,o)||(n.setNode(r,{}),n.setEdge(c,r,{}),e(r))})}),n.nodeCount()}function kr(n,t){return en(t.edges(),function(e){if(n.hasNode(e.v)!==n.hasNode(e.w))return q(t,e)})}function Er(n,t,e){h(n.nodes(),function(c){t.node(c).rank+=e})}function Mn(n,t,e){On(t)||(t=[t]);var c=(n.isDirected()?n.successors:n.neighbors).bind(n),o=[],u={};return h(t,function(r){if(!n.hasNode(r))throw new Error("Graph does not have node: "+r);Tn(n,r,e==="post",u,c,o)}),o}function Tn(n,t,e,c,o,u){Object.prototype.hasOwnProperty.call(c,t)||(c[t]=!0,e||u.push(t),h(o(t),function(r){Tn(n,r,e,c,o,u)}),e&&u.push(t))}function M(n){n=function(c){var o=new R().setGraph(c.graph());return h(c.nodes(),function(u){o.setNode(u,c.node(u))}),h(c.edges(),function(u){var r=o.edge(u.v,u.w)||{weight:0,minlen:1},a=c.edge(u);o.setEdge(u.v,u.w,{weight:r.weight+a.weight,minlen:Math.max(r.minlen,a.minlen)})}),o}(n),an(n);var t,e=Rn(n);for(dn(e),un(e,n);t=Cn(e);)Gn(e,n,t,Sn(e,n,t))}function un(n,t){var e=function(c,o){return Mn(c,o,"post")}(n,n.nodes());e=e.slice(0,e.length-1),h(e,function(c){(function(o,u,r){var a=o.node(r),i=a.parent;o.edge(r,i).cutvalue=jn(o,u,r)})(n,t,c)})}function jn(n,t,e){var c=n.node(e).parent,o=!0,u=t.edge(e,c),r=0;return u||(o=!1,u=t.edge(c,e)),r=u.weight,h(t.nodeEdges(e),function(a){var i,d,f=a.v===e,s=f?a.w:a.v;if(s!==c){var g=f===o,v=t.edge(a).weight;if(r+=g?v:-v,i=e,d=s,n.hasEdge(i,d)){var l=n.edge(e,s).cutvalue;r+=g?-l:l}}}),r}function dn(n,t){arguments.length<2&&(t=n.nodes()[0]),Ln(n,{},1,t)}function Ln(n,t,e,c,o){var u=e,r=n.node(c);return t[c]=!0,h(n.neighbors(c),function(a){Object.prototype.hasOwnProperty.call(t,a)||(e=Ln(n,t,e,a,c))}),r.low=u,r.lim=e++,o?r.parent=o:delete r.parent,e}function Cn(n){return tn(n.edges(),function(t){return n.edge(t).cutvalue<0})}function Sn(n,t,e){var c=e.v,o=e.w;t.hasEdge(c,o)||(c=e.w,o=e.v);var u=n.node(c),r=n.node(o),a=u,i=!1;u.lim>r.lim&&(a=r,i=!0);var d=G(t.edges(),function(f){return i===pn(n,n.node(f.v),a)&&i!==pn(n,n.node(f.w),a)});return en(d,function(f){return q(t,f)})}function Gn(n,t,e,c){var o=e.v,u=e.w;n.removeEdge(o,u),n.setEdge(c.v,c.w,{}),dn(n),un(n,t),function(r,a){var i=tn(r.nodes(),function(f){return!a.node(f).parent}),d=function(f,s){return Mn(f,s,"pre")}(r,i);d=d.slice(1),h(d,function(f){var s=r.node(f).parent,g=a.edge(f,s),v=!1;g||(g=a.edge(s,f),v=!0),a.node(f).rank=a.node(s).rank+(v?g.minlen:-g.minlen)})}(n,t)}function pn(n,t,e){return e.low<=t.lim&&t.lim<=e.lim}function Nr(n){switch(n.graph().ranker){case"network-simplex":default:_r(n);break;case"tight-tree":(function(t){an(t),Rn(t)})(n);break;case"longest-path":Or(n)}}new Error,M.initLowLimValues=dn,M.initCutValues=un,M.calcCutValue=jn,M.leaveEdge=Cn,M.enterEdge=Sn,M.exchangeEdges=Gn;var Or=an;function _r(n){M(n)}function Ir(n){var t=S(n,"root",{},"_root"),e=function(r){var a={};function i(d,f){var s=r.children(d);s&&s.length&&h(s,function(g){i(g,f+1)}),a[d]=f}return h(r.children(),function(d){i(d,1)}),a}(n),c=T(j(e))-1,o=2*c+1;n.graph().nestingRoot=t,h(n.edges(),function(r){n.edge(r).minlen*=o});var u=function(r){return A(r.edges(),function(a,i){return a+r.edge(i).weight},0)}(n)+1;h(n.children(),function(r){Fn(n,t,o,u,c,e,r)}),n.graph().nodeRankFactor=o}function Fn(n,t,e,c,o,u,r){var a=n.children(r);if(a.length){var i=gn(n,"_bt"),d=gn(n,"_bb"),f=n.node(r);n.setParent(i,r),f.borderTop=i,n.setParent(d,r),f.borderBottom=d,h(a,function(s){Fn(n,t,e,c,o,u,s);var g=n.node(s),v=g.borderTop?g.borderTop:s,l=g.borderBottom?g.borderBottom:s,w=g.borderTop?c:2*c,p=v!==l?1:o-u[r]+1;n.setEdge(i,v,{weight:w,minlen:p,nestingEdge:!0}),n.setEdge(l,d,{weight:w,minlen:p,nestingEdge:!0})}),n.parent(r)||n.setEdge(t,i,{weight:0,minlen:o+u[r]})}else r!==t&&n.setEdge(t,r,{weight:0,minlen:e})}function Pr(n,t,e){var c=function(u){for(var r;u.hasNode(r=on("_root")););return r}(n),o=new R({compound:!0}).setGraph({root:c}).setDefaultNodeLabel(function(u){return n.node(u)});return h(n.nodes(),function(u){var r=n.node(u),a=n.parent(u);(r.rank===t||r.minRank<=t&&t<=r.maxRank)&&(o.setNode(u),o.setParent(u,a||c),h(n[e](u),function(i){var d=i.v===u?i.w:i.v,f=o.edge(d,u),s=I(f)?0:f.weight;o.setEdge(d,u,{weight:n.edge(i).weight+s})}),Object.prototype.hasOwnProperty.call(r,"minRank")&&o.setNode(u,{borderLeft:r.borderLeft[t],borderRight:r.borderRight[t]}))}),o}function Rr(n,t){for(var e=0,c=1;c<t.length;++c)e+=Mr(n,t[c-1],t[c]);return e}function Mr(n,t,e){for(var c=function(d,f){return function(s,g,v){for(var l=-1,w=s.length,p=g.length,m={};++l<w;){var b=l<p?g[l]:void 0;v(m,s[l],b)}return m}(d||[],f||[],er)}(e,k(e,function(d,f){return f})),o=C(k(t,function(d){return V(k(n.outEdges(d),function(f){return{pos:c[f.w],weight:n.edge(f).weight}}),"pos")})),u=1;u<e.length;)u<<=1;var r=2*u-1;u-=1;var a=k(new Array(r),function(){return 0}),i=0;return h(o.forEach(function(d){var f=d.pos+u;a[f]+=d.weight;for(var s=0;f>0;)f%2&&(s+=a[f+1]),a[f=f-1>>1]+=d.weight;i+=d.weight*s})),i}function Tr(n,t){var e={};return h(n,function(c,o){var u=e[c.v]={indegree:0,in:[],out:[],vs:[c.v],i:o};I(c.barycenter)||(u.barycenter=c.barycenter,u.weight=c.weight)}),h(t.edges(),function(c){var o=e[c.v],u=e[c.w];I(o)||I(u)||(u.indegree++,o.out.push(e[c.w]))}),function(c){var o=[];function u(i){return function(d){d.merged||(I(d.barycenter)||I(i.barycenter)||d.barycenter>=i.barycenter)&&function(f,s){var g=0,v=0;f.weight&&(g+=f.barycenter*f.weight,v+=f.weight),s.weight&&(g+=s.barycenter*s.weight,v+=s.weight),f.vs=s.vs.concat(f.vs),f.barycenter=g/v,f.weight=v,f.i=Math.min(s.i,f.i),s.merged=!0}(i,d)}}function r(i){return function(d){d.in.push(i),--d.indegree==0&&c.push(d)}}for(;c.length;){var a=c.pop();o.push(a),h(a.in.reverse(),u(a)),h(a.out,r(a))}return k(G(o,function(i){return!i.merged}),function(i){return z(i,["vs","i","barycenter","weight"])})}(G(e,function(c){return!c.indegree}))}function jr(n,t){var e,c=function(s,g){var v={lhs:[],rhs:[]};return h(s,function(l){g(l)?v.lhs.push(l):v.rhs.push(l)}),v}(n,function(s){return Object.prototype.hasOwnProperty.call(s,"barycenter")}),o=c.lhs,u=V(c.rhs,function(s){return-s.i}),r=[],a=0,i=0,d=0;o.sort((e=!!t,function(s,g){return s.barycenter<g.barycenter?-1:s.barycenter>g.barycenter?1:e?g.i-s.i:s.i-g.i})),d=wn(r,u,d),h(o,function(s){d+=s.vs.length,r.push(s.vs),a+=s.barycenter*s.weight,i+=s.weight,d=wn(r,u,d)});var f={vs:C(r)};return i&&(f.barycenter=a/i,f.weight=i),f}function wn(n,t,e){for(var c;t.length&&(c=Y(t)).i<=e;)t.pop(),n.push(c.vs),e++;return e}function Vn(n,t,e,c){var o=n.children(t),u=n.node(t),r=u?u.borderLeft:void 0,a=u?u.borderRight:void 0,i={};r&&(o=G(o,function(l){return l!==r&&l!==a}));var d=function(l,w){return k(w,function(p){var m=l.inEdges(p);if(m.length){var b=A(m,function(y,x){var O=l.edge(x),E=l.node(x.v);return{sum:y.sum+O.weight*E.order,weight:y.weight+O.weight}},{sum:0,weight:0});return{v:p,barycenter:b.sum/b.weight,weight:b.weight}}return{v:p}})}(n,o);h(d,function(l){if(n.children(l.v).length){var w=Vn(n,l.v,e,c);i[l.v]=w,Object.prototype.hasOwnProperty.call(w,"barycenter")&&(m=w,I((p=l).barycenter)?(p.barycenter=m.barycenter,p.weight=m.weight):(p.barycenter=(p.barycenter*p.weight+m.barycenter*m.weight)/(p.weight+m.weight),p.weight+=m.weight))}var p,m});var f=Tr(d,e);(function(l,w){h(l,function(p){p.vs=C(p.vs.map(function(m){return w[m]?w[m].vs:m}))})})(f,i);var s=jr(f,c);if(r&&(s.vs=C([r,s.vs,a]),n.predecessors(r).length)){var g=n.node(n.predecessors(r)[0]),v=n.node(n.predecessors(a)[0]);Object.prototype.hasOwnProperty.call(s,"barycenter")||(s.barycenter=0,s.weight=0),s.barycenter=(s.barycenter*s.weight+g.order+v.order)/(s.weight+2),s.weight+=2}return s}function Lr(n){var t=In(n),e=mn(n,L(1,t+1),"inEdges"),c=mn(n,L(t-1,-1,-1),"outEdges"),o=function(f){var s={},g=G(f.nodes(),function(p){return!f.children(p).length}),v=T(k(g,function(p){return f.node(p).rank})),l=k(L(v+1),function(){return[]}),w=V(g,function(p){return f.node(p).rank});return h(w,function p(m){if(!En(s,m)){s[m]=!0;var b=f.node(m);l[b.rank].push(m),h(f.successors(m),p)}}),l}(n);bn(n,o);for(var u,r=Number.POSITIVE_INFINITY,a=0,i=0;i<4;++a,++i){Cr(a%2?e:c,a%4>=2);var d=Rr(n,o=D(n));d<r&&(i=0,u=Yn(o,ir|ar),r=d)}bn(n,u)}function mn(n,t,e){return k(t,function(c){return Pr(n,c,e)})}function Cr(n,t){var e=new R;h(n,function(c){var o=c.graph().root,u=Vn(c,o,e,t);h(u.vs,function(r,a){c.node(r).order=a}),function(r,a,i){var d,f={};h(i,function(s){for(var g,v,l=r.parent(s);l;){if((g=r.parent(l))?(v=f[g],f[g]=l):(v=d,d=l),v&&v!==l)return void a.setEdge(v,l);l=g}})}(c,e,u.vs)})}function bn(n,t){h(t,function(e){h(e,function(c,o){n.node(c).order=o})})}function Sr(n){var t=function(e){var c={},o=0;function u(r){var a=o;h(e.children(r),u),c[r]={low:a,lim:o++}}return h(e.children(),u),c}(n);h(n.graph().dummyChains,function(e){for(var c=n.node(e),o=c.edgeObj,u=function(s,g,v,l){var w,p,m=[],b=[],y=Math.min(g[v].low,g[l].low),x=Math.max(g[v].lim,g[l].lim);w=v;do w=s.parent(w),m.push(w);while(w&&(g[w].low>y||x>g[w].lim));for(p=w,w=l;(w=s.parent(w))!==p;)b.push(w);return{path:m.concat(b.reverse()),lca:p}}(n,t,o.v,o.w),r=u.path,a=u.lca,i=0,d=r[i],f=!0;e!==o.w;){if(c=n.node(e),f){for(;(d=r[i])!==a&&n.node(d).maxRank<c.rank;)i++;d===a&&(f=!1)}if(!f){for(;i<r.length-1&&n.node(d=r[i+1]).minRank<=c.rank;)i++;d=r[i]}n.setParent(e,d),e=n.successors(e)[0]}})}function Gr(n,t){var e={};return A(t,function(c,o){var u=0,r=0,a=c.length,i=Y(o);return h(o,function(d,f){var s=function(v,l){if(v.node(l).dummy)return tn(v.predecessors(l),function(w){return v.node(w).dummy})}(n,d),g=s?n.node(s).order:a;(s||d===i)&&(h(o.slice(r,f+1),function(v){h(n.predecessors(v),function(l){var w=n.node(l),p=w.order;!(p<u||g<p)||w.dummy&&n.node(v).dummy||qn(e,l,v)})}),r=f+1,u=g)}),o}),e}function qn(n,t,e){if(t>e){var c=t;t=e,e=c}var o=n[t];o||(n[t]=o={}),o[e]=!0}function Fr(n,t,e){if(t>e){var c=t;t=e,e=c}return!!n[t]&&Object.prototype.hasOwnProperty.call(n[t],e)}function Vr(n,t,e,c,o){var u={},r=function(d,f,s,g){var v=new R,l=d.graph(),w=function(p,m,b){return function(y,x,O){var E,_=y.node(x),P=y.node(O),N=0;if(N+=_.width/2,Object.prototype.hasOwnProperty.call(_,"labelpos"))switch(_.labelpos.toLowerCase()){case"l":E=-_.width/2;break;case"r":E=_.width/2}if(E&&(N+=b?E:-E),E=0,N+=(_.dummy?m:p)/2,N+=(P.dummy?m:p)/2,N+=P.width/2,Object.prototype.hasOwnProperty.call(P,"labelpos"))switch(P.labelpos.toLowerCase()){case"l":E=P.width/2;break;case"r":E=-P.width/2}return E&&(N+=b?E:-E),E=0,N}}(l.nodesep,l.edgesep,g);return h(f,function(p){var m;h(p,function(b){var y=s[b];if(v.setNode(y),m){var x=s[m],O=v.edge(x,y);v.setEdge(x,y,Math.max(w(d,b,m),O||0))}m=b})}),v}(n,t,e,o),a=o?"borderLeft":"borderRight";function i(d,f){for(var s=r.nodes(),g=s.pop(),v={};g;)v[g]?d(g):(v[g]=!0,s.push(g),s=s.concat(f(g))),g=s.pop()}return i(function(d){u[d]=r.inEdges(d).reduce(function(f,s){return Math.max(f,u[s.v]+r.edge(s))},0)},r.predecessors.bind(r)),i(function(d){var f=r.outEdges(d).reduce(function(g,v){return Math.min(g,u[v.w]-r.edge(v))},Number.POSITIVE_INFINITY),s=n.node(d);f!==Number.POSITIVE_INFINITY&&s.borderType!==a&&(u[d]=Math.max(u[d],f))},r.successors.bind(r)),h(c,function(d){u[d]=u[e[d]]}),u}function qr(n,t){return en(j(t),function(e){var c,o,u=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY;return o=function(a,i){var d=function(f,s){return f.node(s).width}(n,i)/2;u=Math.max(a+d,u),r=Math.min(a-d,r)},(c=e)==null||Zn(c,yn(o),$n),u-r})}function Br(n){var t,e=D(n),c=$(Gr(n,e),function(r,a){var i={};function d(f,s,g,v,l){var w;h(L(s,g),function(p){w=f[p],r.node(w).dummy&&h(r.predecessors(w),function(m){var b=r.node(m);b.dummy&&(b.order<v||b.order>l)&&qn(i,m,w)})})}return A(a,function(f,s){var g,v=-1,l=0;return h(s,function(w,p){if(r.node(w).dummy==="border"){var m=r.predecessors(w);m.length&&(g=r.node(m[0]).order,d(s,l,p,v,g),l=p,v=g)}d(s,l,s.length,g,f.length)}),s}),i}(n,e)),o={};h(["u","d"],function(r){t=r==="u"?e:j(e).reverse(),h(["l","r"],function(a){a==="r"&&(t=k(t,function(s){return j(s).reverse()}));var i=(r==="u"?n.predecessors:n.successors).bind(n),d=function(s,g,v,l){var w={},p={},m={};return h(g,function(b){h(b,function(y,x){w[y]=y,p[y]=y,m[y]=x})}),h(g,function(b){var y=-1;h(b,function(x){var O=l(x);if(O.length){O=V(O,function(Bn){return m[Bn]});for(var E=(O.length-1)/2,_=Math.floor(E),P=Math.ceil(E);_<=P;++_){var N=O[_];p[x]===x&&y<m[N]&&!Fr(v,x,N)&&(p[N]=x,p[x]=w[x]=w[N],y=m[N])}}})}),{root:w,align:p}}(0,t,c,i),f=Vr(n,t,d.root,d.align,a==="r");a==="r"&&(f=B(f,function(s){return-s})),o[r+a]=f})});var u=qr(n,o);return function(r,a){var i=j(a),d=F(i),f=T(i);h(["u","d"],function(s){h(["l","r"],function(g){var v,l=s+g,w=r[l];if(w!==a){var p=j(w);(v=g==="l"?d-F(p):f-T(p))&&(r[l]=B(w,function(m){return m+v}))}})})}(o,u),function(r,a){return B(r.ul,function(i,d){if(a)return r[a.toLowerCase()][d];var f=V(k(r,d));return(f[1]+f[2])/2})}(o,n.graph().align)}function Yr(n){var t,e;(function(c){var o=D(c),u=c.graph().ranksep,r=0;h(o,function(a){var i=T(k(a,function(d){return c.node(d).height}));h(a,function(d){c.node(d).y=r+i/2}),r+=i+u})})(n=_n(n)),t=Br(n),e=function(c,o){n.node(o).x=c},t&&xn(t,yn(e))}function nt(n,t){var e=mr;e("layout",()=>{var c=e("  buildLayoutGraph",()=>function(o){var u=new R({multigraph:!0,compound:!0}),r=X(o.graph());return u.setGraph($({},Ar,W(r,zr),z(r,Dr))),h(o.nodes(),function(a){var i=X(o.node(a));u.setNode(a,Un(W(i,Jr),Qr)),u.setParent(a,o.parent(a))}),h(o.edges(),function(a){var i=X(o.edge(a));u.setEdge(a,$({},Kr,W(i,Hr),z(i,Ur)))}),u}(n));e("  runLayout",()=>function(o,u){u("    makeSpaceForEdgeLabels",()=>function(r){var a=r.graph();a.ranksep/=2,h(r.edges(),function(i){var d=r.edge(i);d.minlen*=2,d.labelpos.toLowerCase()!=="c"&&(a.rankdir==="TB"||a.rankdir==="BT"?d.width+=d.labeloffset:d.height+=d.labeloffset)})}(o)),u("    removeSelfEdges",()=>function(r){h(r.edges(),function(a){if(a.v===a.w){var i=r.node(a.v);i.selfEdges||(i.selfEdges=[]),i.selfEdges.push({e:a,label:r.edge(a)}),r.removeEdge(a)}})}(o)),u("    acyclic",()=>wr(o)),u("    nestingGraph.run",()=>Ir(o)),u("    rank",()=>Nr(_n(o))),u("    injectEdgeLabelProxies",()=>function(r){h(r.edges(),function(a){var i=r.edge(a);if(i.width&&i.height){var d=r.node(a.v),f={rank:(r.node(a.w).rank-d.rank)/2+d.rank,e:a};S(r,"edge-proxy",f,"_ep")}})}(o)),u("    removeEmptyRanks",()=>function(r){var a=F(k(r.nodes(),function(s){return r.node(s).rank})),i=[];h(r.nodes(),function(s){var g=r.node(s).rank-a;i[g]||(i[g]=[]),i[g].push(s)});var d=0,f=r.graph().nodeRankFactor;h(i,function(s,g){I(s)&&g%f!=0?--d:d&&h(s,function(v){r.node(v).rank+=d})})}(o)),u("    nestingGraph.cleanup",()=>function(r){var a=r.graph();r.removeNode(a.nestingRoot),delete a.nestingRoot,h(r.edges(),function(i){r.edge(i).nestingEdge&&r.removeEdge(i)})}(o)),u("    normalizeRanks",()=>function(r){var a=F(k(r.nodes(),function(i){return r.node(i).rank}));h(r.nodes(),function(i){var d=r.node(i);En(d,"rank")&&(d.rank-=a)})}(o)),u("    assignRankMinMax",()=>function(r){var a=0;h(r.nodes(),function(i){var d=r.node(i);d.borderTop&&(d.minRank=r.node(d.borderTop).rank,d.maxRank=r.node(d.borderBottom).rank,a=T(a,d.maxRank))}),r.graph().maxRank=a}(o)),u("    removeEdgeLabelProxies",()=>function(r){h(r.nodes(),function(a){var i=r.node(a);i.dummy==="edge-proxy"&&(r.edge(i.e).labelRank=i.rank,r.removeNode(a))})}(o)),u("    normalize.run",()=>yr(o)),u("    parentDummyChains",()=>Sr(o)),u("    addBorderSegments",()=>function(r){h(r.children(),function a(i){var d=r.children(i),f=r.node(i);if(d.length&&h(d,a),Object.prototype.hasOwnProperty.call(f,"minRank")){f.borderLeft=[],f.borderRight=[];for(var s=f.minRank,g=f.maxRank+1;s<g;++s)vn(r,"borderLeft","_bl",i,f,s),vn(r,"borderRight","_br",i,f,s)}})}(o)),u("    order",()=>Lr(o)),u("    insertSelfEdges",()=>function(r){var a=D(r);h(a,function(i){var d=0;h(i,function(f,s){var g=r.node(f);g.order=s+d,h(g.selfEdges,function(v){S(r,"selfedge",{width:v.label.width,height:v.label.height,rank:g.rank,order:s+ ++d,e:v.e,label:v.label},"_se")}),delete g.selfEdges})})}(o)),u("    adjustCoordinateSystem",()=>function(r){var a=r.graph().rankdir.toLowerCase();a!=="lr"&&a!=="rl"||Pn(r)}(o)),u("    position",()=>Yr(o)),u("    positionSelfEdges",()=>function(r){h(r.nodes(),function(a){var i=r.node(a);if(i.dummy==="selfedge"){var d=r.node(i.e.v),f=d.x+d.width/2,s=d.y,g=i.x-f,v=d.height/2;r.setEdge(i.e,i.label),r.removeNode(a),i.label.points=[{x:f+2*g/3,y:s-v},{x:f+5*g/6,y:s-v},{x:f+g,y:s},{x:f+5*g/6,y:s+v},{x:f+2*g/3,y:s+v}],i.label.x=i.x,i.label.y=i.y}})}(o)),u("    removeBorderNodes",()=>function(r){h(r.nodes(),function(a){if(r.children(a).length){var i=r.node(a),d=r.node(i.borderTop),f=r.node(i.borderBottom),s=r.node(Y(i.borderLeft)),g=r.node(Y(i.borderRight));i.width=Math.abs(g.x-s.x),i.height=Math.abs(f.y-d.y),i.x=s.x+i.width/2,i.y=d.y+i.height/2}}),h(r.nodes(),function(a){r.node(a).dummy==="border"&&r.removeNode(a)})}(o)),u("    normalize.undo",()=>function(r){h(r.graph().dummyChains,function(a){var i,d=r.node(a),f=d.edgeLabel;for(r.setEdge(d.edgeObj,f);d.dummy;)i=r.successors(a)[0],r.removeNode(a),f.points.push({x:d.x,y:d.y}),d.dummy==="edge-label"&&(f.x=d.x,f.y=d.y,f.width=d.width,f.height=d.height),a=i,d=r.node(a)})}(o)),u("    fixupEdgeLabelCoords",()=>function(r){h(r.edges(),function(a){var i=r.edge(a);if(Object.prototype.hasOwnProperty.call(i,"x"))switch(i.labelpos!=="l"&&i.labelpos!=="r"||(i.width-=i.labeloffset),i.labelpos){case"l":i.x-=i.width/2+i.labeloffset;break;case"r":i.x+=i.width/2+i.labeloffset}})}(o)),u("    undoCoordinateSystem",()=>br(o)),u("    translateGraph",()=>function(r){var a=Number.POSITIVE_INFINITY,i=0,d=Number.POSITIVE_INFINITY,f=0,s=r.graph(),g=s.marginx||0,v=s.marginy||0;function l(w){var p=w.x,m=w.y,b=w.width,y=w.height;a=Math.min(a,p-b/2),i=Math.max(i,p+b/2),d=Math.min(d,m-y/2),f=Math.max(f,m+y/2)}h(r.nodes(),function(w){l(r.node(w))}),h(r.edges(),function(w){var p=r.edge(w);Object.prototype.hasOwnProperty.call(p,"x")&&l(p)}),a-=g,d-=v,h(r.nodes(),function(w){var p=r.node(w);p.x-=a,p.y-=d}),h(r.edges(),function(w){var p=r.edge(w);h(p.points,function(m){m.x-=a,m.y-=d}),Object.prototype.hasOwnProperty.call(p,"x")&&(p.x-=a),Object.prototype.hasOwnProperty.call(p,"y")&&(p.y-=d)}),s.width=i-a+g,s.height=f-d+v}(o)),u("    assignNodeIntersects",()=>function(r){h(r.edges(),function(a){var i,d,f=r.edge(a),s=r.node(a.v),g=r.node(a.w);f.points?(i=f.points[0],d=f.points[f.points.length-1]):(f.points=[],i=g,d=s),f.points.unshift(hn(s,i)),f.points.push(hn(g,d))})}(o)),u("    reversePoints",()=>function(r){h(r.edges(),function(a){var i=r.edge(a);i.reversed&&i.points.reverse()})}(o)),u("    acyclic.undo",()=>function(r){h(r.edges(),function(a){var i=r.edge(a);if(i.reversed){r.removeEdge(a);var d=i.forwardName;delete i.reversed,delete i.forwardName,r.setEdge(a.w,a.v,i,d)}})}(o))}(c,e)),e("  updateInputGraph",()=>function(o,u){h(o.nodes(),function(r){var a=o.node(r),i=u.node(r);a&&(a.x=i.x,a.y=i.y,u.children(r).length&&(a.width=i.width,a.height=i.height))}),h(o.edges(),function(r){var a=o.edge(r),i=u.edge(r);a.points=i.points,Object.prototype.hasOwnProperty.call(i,"x")&&(a.x=i.x,a.y=i.y)}),o.graph().width=u.graph().width,o.graph().height=u.graph().height}(n,c))})}var zr=["nodesep","edgesep","ranksep","marginx","marginy"],Ar={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},Dr=["acyclicer","ranker","rankdir","align"],Jr=["width","height"],Qr={width:0,height:0},Hr=["minlen","weight","width","height","labeloffset"],Kr={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},Ur=["labelpos"];function W(n,t){return B(z(n,t),Number)}function X(n){var t={};return h(n,function(e,c){t[c.toLowerCase()]=e}),t}export{nt as l};

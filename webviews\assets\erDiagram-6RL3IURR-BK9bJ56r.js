import{_ as d,o as mt,p as ft,g as Et,s as gt,a as bt,b as Ot,c as F,l as H,d as ct,u as kt,e as Rt,x as Nt,a1 as xt,a8 as Tt,a7 as At}from"./AugmentMessage-bvgL2GJ0.js";import{G as Mt}from"./graph-KsABUsDv.js";import{l as wt}from"./layout-Bax-EyfV.js";import"./SpinnerAugment-BejxPZX4.js";import"./IconButtonAugment-DmfIVAYG.js";import"./CalloutAugment-DH8_XiUe.js";import"./CardAugment-dQY7gB6x.js";import"./index-LNVO_dOZ.js";import"./async-messaging-D7f6isRQ.js";import"./message-broker-B_Xbv83k.js";import"./types-CGlLNakm.js";import"./file-paths-B4wu8Zer.js";import"./BaseTextInput-B8Zm7GJJ.js";import"./folder-opened-ChvTYBfK.js";import"./index-BlaqVU85.js";import"./diff-operations-BLNMYUZC.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-DSk2r097.js";import"./index-6AlVWKh5.js";import"./keypress-DD1aQVr0.js";import"./await_block-Dt1rTGTc.js";import"./OpenFileButton-DltEK_xL.js";import"./chat-context-CmoAn9pE.js";import"./index-B528snJk.js";import"./remote-agents-client-DwCOg82s.js";import"./ra-diff-ops-model-DHtiwZCr.js";import"./TextAreaAugment-DCLSWWW0.js";import"./ButtonAugment-CVatBELe.js";import"./CollapseButtonAugment-03DKN6zM.js";import"./partner-mcp-utils-PxcJW8dJ.js";import"./MaterialIcon-CUEAtZVx.js";import"./CopyButton-Cb58npax.js";import"./copy-BA1J_YQn.js";import"./ellipsis-BRv5sK55.js";import"./IconFilePath-BUMTFdYT.js";import"./LanguageIcon-B9tL-FlO.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-DMucGkIH.js";import"./index-Dkoz80k5.js";import"./augment-logo-BRK9m6wM.js";import"./pen-to-square-5PNRCAQu.js";import"./chevron-down-D78W1-g3.js";import"./check-Cc9E51ln.js";import"./_baseUniq-Dec7a2Hj.js";import"./_basePickBy-DktbUoK0.js";const It=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,M=[];for(let t=0;t<256;++t)M.push((t+256).toString(16).slice(1));function St(t){if(!function(p){return typeof p=="string"&&It.test(p)}(t))throw TypeError("Invalid UUID");let r;const e=new Uint8Array(16);return e[0]=(r=parseInt(t.slice(0,8),16))>>>24,e[1]=r>>>16&255,e[2]=r>>>8&255,e[3]=255&r,e[4]=(r=parseInt(t.slice(9,13),16))>>>8,e[5]=255&r,e[6]=(r=parseInt(t.slice(14,18),16))>>>8,e[7]=255&r,e[8]=(r=parseInt(t.slice(19,23),16))>>>8,e[9]=255&r,e[10]=(r=parseInt(t.slice(24,36),16))/1099511627776&255,e[11]=r/4294967296&255,e[12]=r>>>24&255,e[13]=r>>>16&255,e[14]=r>>>8&255,e[15]=255&r,e}function Dt(t,r,e,p){switch(t){case 0:return r&e^~r&p;case 1:case 3:return r^e^p;case 2:return r&e^r&p^e&p}}function tt(t,r){return t<<r|t>>>32-r}const vt=function(t,r,e){function p(n,f,u,c){var y;if(typeof n=="string"&&(n=function(s){s=unescape(encodeURIComponent(s));const _=[];for(let O=0;O<s.length;++O)_.push(s.charCodeAt(O));return _}(n)),typeof f=="string"&&(f=St(f)),((y=f)===null||y===void 0?void 0:y.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let b=new Uint8Array(16+n.length);if(b.set(f),b.set(n,f.length),b=e(b),b[6]=15&b[6]|r,b[8]=63&b[8]|128,u){c=c||0;for(let s=0;s<16;++s)u[c+s]=b[s];return u}return function(s,_=0){return M[s[_+0]]+M[s[_+1]]+M[s[_+2]]+M[s[_+3]]+"-"+M[s[_+4]]+M[s[_+5]]+"-"+M[s[_+6]]+M[s[_+7]]+"-"+M[s[_+8]]+M[s[_+9]]+"-"+M[s[_+10]]+M[s[_+11]]+M[s[_+12]]+M[s[_+13]]+M[s[_+14]]+M[s[_+15]]}(b)}try{p.name=t}catch{}return p.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",p.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",p}("v5",80,function(t){const r=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof t=="string"){const u=unescape(encodeURIComponent(t));t=[];for(let c=0;c<u.length;++c)t.push(u.charCodeAt(c))}else Array.isArray(t)||(t=Array.prototype.slice.call(t));t.push(128);const p=t.length/4+2,n=Math.ceil(p/16),f=new Array(n);for(let u=0;u<n;++u){const c=new Uint32Array(16);for(let y=0;y<16;++y)c[y]=t[64*u+4*y]<<24|t[64*u+4*y+1]<<16|t[64*u+4*y+2]<<8|t[64*u+4*y+3];f[u]=c}f[n-1][14]=8*(t.length-1)/Math.pow(2,32),f[n-1][14]=Math.floor(f[n-1][14]),f[n-1][15]=8*(t.length-1)&4294967295;for(let u=0;u<n;++u){const c=new Uint32Array(80);for(let E=0;E<16;++E)c[E]=f[u][E];for(let E=16;E<80;++E)c[E]=tt(c[E-3]^c[E-8]^c[E-14]^c[E-16],1);let y=e[0],b=e[1],s=e[2],_=e[3],O=e[4];for(let E=0;E<80;++E){const T=Math.floor(E/20),w=tt(y,5)+Dt(T,b,s,_)+O+r[T]+c[E]>>>0;O=_,_=s,s=tt(b,30)>>>0,b=y,y=w}e[0]=e[0]+y>>>0,e[1]=e[1]+b>>>0,e[2]=e[2]+s>>>0,e[3]=e[3]+_>>>0,e[4]=e[4]+O>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,255&e[0],e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,255&e[1],e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,255&e[2],e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,255&e[3],e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,255&e[4]]});var et=function(){var t=d(function(a,m,h,o){for(h=h||{},o=a.length;o--;h[a[o]]=m);return h},"o"),r=[6,8,10,20,22,24,26,27,28],e=[1,10],p=[1,11],n=[1,12],f=[1,13],u=[1,14],c=[1,15],y=[1,21],b=[1,22],s=[1,23],_=[1,24],O=[1,25],E=[6,8,10,13,15,18,19,20,22,24,26,27,28,41,42,43,44,45],T=[1,34],w=[27,28,46,47],C=[41,42,43,44,45],P=[17,34],Y=[1,54],A=[1,53],I=[17,34,36,38],R={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,ER_DIAGRAM:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,entityName:11,relSpec:12,":":13,role:14,BLOCK_START:15,attributes:16,BLOCK_STOP:17,SQS:18,SQE:19,title:20,title_value:21,acc_title:22,acc_title_value:23,acc_descr:24,acc_descr_value:25,acc_descr_multiline_value:26,ALPHANUM:27,ENTITY_NAME:28,attribute:29,attributeType:30,attributeName:31,attributeKeyTypeList:32,attributeComment:33,ATTRIBUTE_WORD:34,attributeKeyType:35,COMMA:36,ATTRIBUTE_KEY:37,COMMENT:38,cardinality:39,relType:40,ZERO_OR_ONE:41,ZERO_OR_MORE:42,ONE_OR_MORE:43,ONLY_ONE:44,MD_PARENT:45,NON_IDENTIFYING:46,IDENTIFYING:47,WORD:48,$accept:0,$end:1},terminals_:{2:"error",4:"ER_DIAGRAM",6:"EOF",8:"SPACE",10:"NEWLINE",13:":",15:"BLOCK_START",17:"BLOCK_STOP",18:"SQS",19:"SQE",20:"title",21:"title_value",22:"acc_title",23:"acc_title_value",24:"acc_descr",25:"acc_descr_value",26:"acc_descr_multiline_value",27:"ALPHANUM",28:"ENTITY_NAME",34:"ATTRIBUTE_WORD",36:"COMMA",37:"ATTRIBUTE_KEY",38:"COMMENT",41:"ZERO_OR_ONE",42:"ZERO_OR_MORE",43:"ONE_OR_MORE",44:"ONLY_ONE",45:"MD_PARENT",46:"NON_IDENTIFYING",47:"IDENTIFYING",48:"WORD"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,4],[9,3],[9,1],[9,7],[9,6],[9,4],[9,2],[9,2],[9,2],[9,1],[11,1],[11,1],[16,1],[16,2],[29,2],[29,3],[29,3],[29,4],[30,1],[31,1],[32,1],[32,3],[35,1],[33,1],[12,3],[39,1],[39,1],[39,1],[39,1],[39,1],[40,1],[40,1],[14,1],[14,1],[14,1]],performAction:d(function(a,m,h,o,g,i,z){var l=i.length-1;switch(g){case 1:break;case 2:case 6:case 7:this.$=[];break;case 3:i[l-1].push(i[l]),this.$=i[l-1];break;case 4:case 5:case 19:case 43:case 27:case 28:case 31:this.$=i[l];break;case 8:o.addEntity(i[l-4]),o.addEntity(i[l-2]),o.addRelationship(i[l-4],i[l],i[l-2],i[l-3]);break;case 9:o.addEntity(i[l-3]),o.addAttributes(i[l-3],i[l-1]);break;case 10:o.addEntity(i[l-2]);break;case 11:o.addEntity(i[l]);break;case 12:o.addEntity(i[l-6],i[l-4]),o.addAttributes(i[l-6],i[l-1]);break;case 13:o.addEntity(i[l-5],i[l-3]);break;case 14:o.addEntity(i[l-3],i[l-1]);break;case 15:case 16:this.$=i[l].trim(),o.setAccTitle(this.$);break;case 17:case 18:this.$=i[l].trim(),o.setAccDescription(this.$);break;case 20:case 41:case 42:case 32:this.$=i[l].replace(/"/g,"");break;case 21:case 29:this.$=[i[l]];break;case 22:i[l].push(i[l-1]),this.$=i[l];break;case 23:this.$={attributeType:i[l-1],attributeName:i[l]};break;case 24:this.$={attributeType:i[l-2],attributeName:i[l-1],attributeKeyTypeList:i[l]};break;case 25:this.$={attributeType:i[l-2],attributeName:i[l-1],attributeComment:i[l]};break;case 26:this.$={attributeType:i[l-3],attributeName:i[l-2],attributeKeyTypeList:i[l-1],attributeComment:i[l]};break;case 30:i[l-2].push(i[l]),this.$=i[l-2];break;case 33:this.$={cardA:i[l],relType:i[l-1],cardB:i[l-2]};break;case 34:this.$=o.Cardinality.ZERO_OR_ONE;break;case 35:this.$=o.Cardinality.ZERO_OR_MORE;break;case 36:this.$=o.Cardinality.ONE_OR_MORE;break;case 37:this.$=o.Cardinality.ONLY_ONE;break;case 38:this.$=o.Cardinality.MD_PARENT;break;case 39:this.$=o.Identification.NON_IDENTIFYING;break;case 40:this.$=o.Identification.IDENTIFYING}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(r,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,20:e,22:p,24:n,26:f,27:u,28:c},t(r,[2,7],{1:[2,1]}),t(r,[2,3]),{9:16,11:9,20:e,22:p,24:n,26:f,27:u,28:c},t(r,[2,5]),t(r,[2,6]),t(r,[2,11],{12:17,39:20,15:[1,18],18:[1,19],41:y,42:b,43:s,44:_,45:O}),{21:[1,26]},{23:[1,27]},{25:[1,28]},t(r,[2,18]),t(E,[2,19]),t(E,[2,20]),t(r,[2,4]),{11:29,27:u,28:c},{16:30,17:[1,31],29:32,30:33,34:T},{11:35,27:u,28:c},{40:36,46:[1,37],47:[1,38]},t(w,[2,34]),t(w,[2,35]),t(w,[2,36]),t(w,[2,37]),t(w,[2,38]),t(r,[2,15]),t(r,[2,16]),t(r,[2,17]),{13:[1,39]},{17:[1,40]},t(r,[2,10]),{16:41,17:[2,21],29:32,30:33,34:T},{31:42,34:[1,43]},{34:[2,27]},{19:[1,44]},{39:45,41:y,42:b,43:s,44:_,45:O},t(C,[2,39]),t(C,[2,40]),{14:46,27:[1,49],28:[1,48],48:[1,47]},t(r,[2,9]),{17:[2,22]},t(P,[2,23],{32:50,33:51,35:52,37:Y,38:A}),t([17,34,37,38],[2,28]),t(r,[2,14],{15:[1,55]}),t([27,28],[2,33]),t(r,[2,8]),t(r,[2,41]),t(r,[2,42]),t(r,[2,43]),t(P,[2,24],{33:56,36:[1,57],38:A}),t(P,[2,25]),t(I,[2,29]),t(P,[2,32]),t(I,[2,31]),{16:58,17:[1,59],29:32,30:33,34:T},t(P,[2,26]),{35:60,37:Y},{17:[1,61]},t(r,[2,13]),t(I,[2,30]),t(r,[2,12])],defaultActions:{34:[2,27],41:[2,22]},parseError:d(function(a,m){if(!m.recoverable){var h=new Error(a);throw h.hash=m,h}this.trace(a)},"parseError"),parse:d(function(a){var m=this,h=[0],o=[],g=[null],i=[],z=this.table,l="",j=0,at=0,yt=i.slice.call(arguments,1),x=Object.create(this.lexer),W={yy:{}};for(var V in this.yy)Object.prototype.hasOwnProperty.call(this.yy,V)&&(W.yy[V]=this.yy[V]);x.setInput(a,W.yy),W.yy.lexer=x,W.yy.parser=this,x.yylloc===void 0&&(x.yylloc={});var J=x.yylloc;i.push(J);var _t=x.options&&x.options.ranges;function nt(){var L;return typeof(L=o.pop()||x.lex()||1)!="number"&&(L instanceof Array&&(L=(o=L).pop()),L=m.symbols_[L]||L),L}typeof W.yy.parseError=="function"?this.parseError=W.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,d(function(L){h.length=h.length-2*L,g.length=g.length-L,i.length=i.length-L},"popStack"),d(nt,"lex");for(var S,U,v,st,X,Z,ot,q,G={};;){if(U=h[h.length-1],this.defaultActions[U]?v=this.defaultActions[U]:(S==null&&(S=nt()),v=z[U]&&z[U][S]),v===void 0||!v.length||!v[0]){var lt="";for(X in q=[],z[U])this.terminals_[X]&&X>2&&q.push("'"+this.terminals_[X]+"'");lt=x.showPosition?"Parse error on line "+(j+1)+`:
`+x.showPosition()+`
Expecting `+q.join(", ")+", got '"+(this.terminals_[S]||S)+"'":"Parse error on line "+(j+1)+": Unexpected "+(S==1?"end of input":"'"+(this.terminals_[S]||S)+"'"),this.parseError(lt,{text:x.match,token:this.terminals_[S]||S,line:x.yylineno,loc:J,expected:q})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+U+", token: "+S);switch(v[0]){case 1:h.push(S),g.push(x.yytext),i.push(x.yylloc),h.push(v[1]),S=null,at=x.yyleng,l=x.yytext,j=x.yylineno,J=x.yylloc;break;case 2:if(Z=this.productions_[v[1]][1],G.$=g[g.length-Z],G._$={first_line:i[i.length-(Z||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(Z||1)].first_column,last_column:i[i.length-1].last_column},_t&&(G._$.range=[i[i.length-(Z||1)].range[0],i[i.length-1].range[1]]),(st=this.performAction.apply(G,[l,at,j,W.yy,v[1],g,i].concat(yt)))!==void 0)return st;Z&&(h=h.slice(0,-1*Z*2),g=g.slice(0,-1*Z),i=i.slice(0,-1*Z)),h.push(this.productions_[v[1]][0]),g.push(G.$),i.push(G._$),ot=z[h[h.length-2]][h[h.length-1]],h.push(ot);break;case 3:return!0}}return!0},"parse")},N=function(){return{EOF:1,parseError:d(function(a,m){if(!this.yy.parser)throw new Error(a);this.yy.parser.parseError(a,m)},"parseError"),setInput:d(function(a,m){return this.yy=m||this.yy||{},this._input=a,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var a=this._input[0];return this.yytext+=a,this.yyleng++,this.offset++,this.match+=a,this.matched+=a,a.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),a},"input"),unput:d(function(a){var m=a.length,h=a.split(/(?:\r\n?|\n)/g);this._input=a+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-m),this.offset-=m;var o=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),h.length-1&&(this.yylineno-=h.length-1);var g=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:h?(h.length===o.length?this.yylloc.first_column:0)+o[o.length-h.length].length-h[0].length:this.yylloc.first_column-m},this.options.ranges&&(this.yylloc.range=[g[0],g[0]+this.yyleng-m]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:d(function(a){this.unput(this.match.slice(a))},"less"),pastInput:d(function(){var a=this.matched.substr(0,this.matched.length-this.match.length);return(a.length>20?"...":"")+a.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var a=this.match;return a.length<20&&(a+=this._input.substr(0,20-a.length)),(a.substr(0,20)+(a.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var a=this.pastInput(),m=new Array(a.length+1).join("-");return a+this.upcomingInput()+`
`+m+"^"},"showPosition"),test_match:d(function(a,m){var h,o,g;if(this.options.backtrack_lexer&&(g={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(g.yylloc.range=this.yylloc.range.slice(0))),(o=a[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=o.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:o?o[o.length-1].length-o[o.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+a[0].length},this.yytext+=a[0],this.match+=a[0],this.matches=a,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(a[0].length),this.matched+=a[0],h=this.performAction.call(this,this.yy,this,m,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),h)return h;if(this._backtrack){for(var i in g)this[i]=g[i];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;var a,m,h,o;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var g=this._currentRules(),i=0;i<g.length;i++)if((h=this._input.match(this.rules[g[i]]))&&(!m||h[0].length>m[0].length)){if(m=h,o=i,this.options.backtrack_lexer){if((a=this.test_match(h,g[i]))!==!1)return a;if(this._backtrack){m=!1;continue}return!1}if(!this.options.flex)break}return m?(a=this.test_match(m,g[o]))!==!1&&a:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var a=this.next();return a||this.lex()},"lex"),begin:d(function(a){this.conditionStack.push(a)},"begin"),popState:d(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(a){return(a=this.conditionStack.length-1-Math.abs(a||0))>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:d(function(a){this.begin(a)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(a,m,h,o){switch(h){case 0:return this.begin("acc_title"),22;case 1:return this.popState(),"acc_title_value";case 2:return this.begin("acc_descr"),24;case 3:return this.popState(),"acc_descr_value";case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 10;case 8:case 15:case 20:break;case 9:return 8;case 10:return 28;case 11:return 48;case 12:return 4;case 13:return this.begin("block"),15;case 14:return 36;case 16:return 37;case 17:case 18:return 34;case 19:return 38;case 21:return this.popState(),17;case 22:case 54:return m.yytext[0];case 23:return 18;case 24:return 19;case 25:case 29:case 30:case 43:return 41;case 26:case 27:case 28:case 36:case 38:case 45:return 43;case 31:case 32:case 33:case 34:case 35:case 37:case 44:return 42;case 39:case 40:case 41:case 42:return 44;case 46:return 45;case 47:case 50:case 51:case 52:return 46;case 48:case 49:return 47;case 53:return 27;case 55:return 6}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:[\s]+)/i,/^(?:"[^"%\r\n\v\b\\]+")/i,/^(?:"[^"]*")/i,/^(?:erDiagram\b)/i,/^(?:\{)/i,/^(?:,)/i,/^(?:\s+)/i,/^(?:\b((?:PK)|(?:FK)|(?:UK))\b)/i,/^(?:(.*?)[~](.*?)*[~])/i,/^(?:[\*A-Za-z_][A-Za-z0-9\-_\[\]\(\)]*)/i,/^(?:"[^"]*")/i,/^(?:[\n]+)/i,/^(?:\})/i,/^(?:.)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:one or zero\b)/i,/^(?:one or more\b)/i,/^(?:one or many\b)/i,/^(?:1\+)/i,/^(?:\|o\b)/i,/^(?:zero or one\b)/i,/^(?:zero or more\b)/i,/^(?:zero or many\b)/i,/^(?:0\+)/i,/^(?:\}o\b)/i,/^(?:many\(0\))/i,/^(?:many\(1\))/i,/^(?:many\b)/i,/^(?:\}\|)/i,/^(?:one\b)/i,/^(?:only one\b)/i,/^(?:1\b)/i,/^(?:\|\|)/i,/^(?:o\|)/i,/^(?:o\{)/i,/^(?:\|\{)/i,/^(?:\s*u\b)/i,/^(?:\.\.)/i,/^(?:--)/i,/^(?:to\b)/i,/^(?:optionally to\b)/i,/^(?:\.-)/i,/^(?:-\.)/i,/^(?:[A-Za-z_][A-Za-z0-9\-_]*)/i,/^(?:.)/i,/^(?:$)/i],conditions:{acc_descr_multiline:{rules:[5,6],inclusive:!1},acc_descr:{rules:[3],inclusive:!1},acc_title:{rules:[1],inclusive:!1},block:{rules:[14,15,16,17,18,19,20,21,22],inclusive:!1},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55],inclusive:!0}}}}();function D(){this.yy={}}return R.lexer=N,d(D,"Parser"),D.prototype=R,R.Parser=D,new D}();et.parser=et;var Lt=et,K=new Map,it=[],dt=d(function(t,r=void 0){return K.has(t)?!K.get(t).alias&&r&&(K.get(t).alias=r,H.info(`Add alias '${r}' to entity '${t}'`)):(K.set(t,{attributes:[],alias:r}),H.info("Added new entity :",t)),K.get(t)},"addEntity"),$t=d(()=>K,"getEntities"),Bt=d(function(t,r){let e,p=dt(t);for(e=r.length-1;e>=0;e--)p.attributes.push(r[e]),H.debug("Added attribute ",r[e].attributeName)},"addAttributes"),Ct=d(function(t,r,e,p){let n={entityA:t,roleA:r,entityB:e,relSpec:p};it.push(n),H.debug("Added new relationship :",n)},"addRelationship"),Pt=d(()=>it,"getRelationships"),Yt=d(function(){K=new Map,it=[],Nt()},"clear"),Zt={Cardinality:{ZERO_OR_ONE:"ZERO_OR_ONE",ZERO_OR_MORE:"ZERO_OR_MORE",ONE_OR_MORE:"ONE_OR_MORE",ONLY_ONE:"ONLY_ONE",MD_PARENT:"MD_PARENT"},Identification:{NON_IDENTIFYING:"NON_IDENTIFYING",IDENTIFYING:"IDENTIFYING"},getConfig:d(()=>F().er,"getConfig"),addEntity:dt,addAttributes:Bt,getEntities:$t,addRelationship:Ct,getRelationships:Pt,clear:Yt,setAccTitle:Ot,getAccTitle:bt,setAccDescription:gt,getAccDescription:Et,setDiagramTitle:ft,getDiagramTitle:mt},$={ONLY_ONE_START:"ONLY_ONE_START",ONLY_ONE_END:"ONLY_ONE_END",ZERO_OR_ONE_START:"ZERO_OR_ONE_START",ZERO_OR_ONE_END:"ZERO_OR_ONE_END",ONE_OR_MORE_START:"ONE_OR_MORE_START",ONE_OR_MORE_END:"ONE_OR_MORE_END",ZERO_OR_MORE_START:"ZERO_OR_MORE_START",ZERO_OR_MORE_END:"ZERO_OR_MORE_END",MD_PARENT_END:"MD_PARENT_END",MD_PARENT_START:"MD_PARENT_START"},B={ERMarkers:$,insertMarkers:d(function(t,r){let e;t.append("defs").append("marker").attr("id",$.MD_PARENT_START).attr("refX",0).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",$.MD_PARENT_END).attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",$.ONLY_ONE_START).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M9,0 L9,18 M15,0 L15,18"),t.append("defs").append("marker").attr("id",$.ONLY_ONE_END).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M3,0 L3,18 M9,0 L9,18"),e=t.append("defs").append("marker").attr("id",$.ZERO_OR_ONE_START).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto"),e.append("circle").attr("stroke",r.stroke).attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),e.append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M9,0 L9,18"),e=t.append("defs").append("marker").attr("id",$.ZERO_OR_ONE_END).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto"),e.append("circle").attr("stroke",r.stroke).attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),e.append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M21,0 L21,18"),t.append("defs").append("marker").attr("id",$.ONE_OR_MORE_START).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),t.append("defs").append("marker").attr("id",$.ONE_OR_MORE_END).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"),e=t.append("defs").append("marker").attr("id",$.ZERO_OR_MORE_START).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto"),e.append("circle").attr("stroke",r.stroke).attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),e.append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18"),e=t.append("defs").append("marker").attr("id",$.ZERO_OR_MORE_END).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto"),e.append("circle").attr("stroke",r.stroke).attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),e.append("path").attr("stroke",r.stroke).attr("fill","none").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"insertMarkers")},Ft=/[^\dA-Za-z](\W)*/g,k={},Q=new Map,zt=d(function(t){const r=Object.keys(t);for(const e of r)k[e]=t[e]},"setConf"),Wt=d((t,r,e)=>{const p=k.entityPadding/3,n=k.entityPadding/3,f=.85*k.fontSize,u=r.node().getBBox(),c=[];let y=!1,b=!1,s=0,_=0,O=0,E=0,T=u.height+2*p,w=1;e.forEach(A=>{A.attributeKeyTypeList!==void 0&&A.attributeKeyTypeList.length>0&&(y=!0),A.attributeComment!==void 0&&(b=!0)}),e.forEach(A=>{const I=`${r.node().id}-attr-${w}`;let R=0;const N=At(A.attributeType),D=t.append("text").classed("er entityLabel",!0).attr("id",`${I}-type`).attr("x",0).attr("y",0).style("dominant-baseline","middle").style("text-anchor","left").style("font-family",F().fontFamily).style("font-size",f+"px").text(N),a=t.append("text").classed("er entityLabel",!0).attr("id",`${I}-name`).attr("x",0).attr("y",0).style("dominant-baseline","middle").style("text-anchor","left").style("font-family",F().fontFamily).style("font-size",f+"px").text(A.attributeName),m={};m.tn=D,m.nn=a;const h=D.node().getBBox(),o=a.node().getBBox();if(s=Math.max(s,h.width),_=Math.max(_,o.width),R=Math.max(h.height,o.height),y){const g=A.attributeKeyTypeList!==void 0?A.attributeKeyTypeList.join(","):"",i=t.append("text").classed("er entityLabel",!0).attr("id",`${I}-key`).attr("x",0).attr("y",0).style("dominant-baseline","middle").style("text-anchor","left").style("font-family",F().fontFamily).style("font-size",f+"px").text(g);m.kn=i;const z=i.node().getBBox();O=Math.max(O,z.width),R=Math.max(R,z.height)}if(b){const g=t.append("text").classed("er entityLabel",!0).attr("id",`${I}-comment`).attr("x",0).attr("y",0).style("dominant-baseline","middle").style("text-anchor","left").style("font-family",F().fontFamily).style("font-size",f+"px").text(A.attributeComment||"");m.cn=g;const i=g.node().getBBox();E=Math.max(E,i.width),R=Math.max(R,i.height)}m.height=R,c.push(m),T+=R+2*p,w+=1});let C=4;y&&(C+=2),b&&(C+=2);const P=s+_+O+E,Y={width:Math.max(k.minEntityWidth,Math.max(u.width+2*k.entityPadding,P+n*C)),height:e.length>0?T:Math.max(k.minEntityHeight,u.height+2*k.entityPadding)};if(e.length>0){const A=Math.max(0,(Y.width-P-n*C)/(C/2));r.attr("transform","translate("+Y.width/2+","+(p+u.height/2)+")");let I=u.height+2*p,R="attributeBoxOdd";c.forEach(N=>{const D=I+p+N.height/2;N.tn.attr("transform","translate("+n+","+D+")");const a=t.insert("rect","#"+N.tn.node().id).classed(`er ${R}`,!0).attr("x",0).attr("y",I).attr("width",s+2*n+A).attr("height",N.height+2*p),m=parseFloat(a.attr("x"))+parseFloat(a.attr("width"));N.nn.attr("transform","translate("+(m+n)+","+D+")");const h=t.insert("rect","#"+N.nn.node().id).classed(`er ${R}`,!0).attr("x",m).attr("y",I).attr("width",_+2*n+A).attr("height",N.height+2*p);let o=parseFloat(h.attr("x"))+parseFloat(h.attr("width"));if(y){N.kn.attr("transform","translate("+(o+n)+","+D+")");const g=t.insert("rect","#"+N.kn.node().id).classed(`er ${R}`,!0).attr("x",o).attr("y",I).attr("width",O+2*n+A).attr("height",N.height+2*p);o=parseFloat(g.attr("x"))+parseFloat(g.attr("width"))}b&&(N.cn.attr("transform","translate("+(o+n)+","+D+")"),t.insert("rect","#"+N.cn.node().id).classed(`er ${R}`,"true").attr("x",o).attr("y",I).attr("width",E+2*n+A).attr("height",N.height+2*p)),I+=N.height+2*p,R=R==="attributeBoxOdd"?"attributeBoxEven":"attributeBoxOdd"})}else Y.height=Math.max(k.minEntityHeight,T),r.attr("transform","translate("+Y.width/2+","+Y.height/2+")");return Y},"drawAttributes"),Ut=d(function(t,r,e){let p;return[...r.keys()].forEach(function(n){const f=ut(n,"entity");Q.set(n,f);const u=t.append("g").attr("id",f);p=p===void 0?f:p;const c="text-"+f,y=u.append("text").classed("er entityLabel",!0).attr("id",c).attr("x",0).attr("y",0).style("dominant-baseline","middle").style("text-anchor","middle").style("font-family",F().fontFamily).style("font-size",k.fontSize+"px").text(r.get(n).alias??n),{width:b,height:s}=Wt(u,y,r.get(n).attributes),_=u.insert("rect","#"+c).classed("er entityBox",!0).attr("x",0).attr("y",0).attr("width",b).attr("height",s).node().getBBox();e.setNode(f,{width:_.width,height:_.height,shape:"rect",id:f})}),p},"drawEntities"),Kt=d(function(t,r){r.nodes().forEach(function(e){e!==void 0&&r.node(e)!==void 0&&t.select("#"+e).attr("transform","translate("+(r.node(e).x-r.node(e).width/2)+","+(r.node(e).y-r.node(e).height/2)+" )")})},"adjustEntities"),pt=d(function(t){return(t.entityA+t.roleA+t.entityB).replace(/\s/g,"")},"getEdgeName"),Gt=d(function(t,r){return t.forEach(function(e){r.setEdge(Q.get(e.entityA),Q.get(e.entityB),{relationship:e},pt(e))}),t},"addRelationships"),ht=0,Ht=d(function(t,r,e,p,n){ht++;const f=e.edge(Q.get(r.entityA),Q.get(r.entityB),pt(r)),u=xt().x(function(w){return w.x}).y(function(w){return w.y}).curve(Tt),c=t.insert("path","#"+p).classed("er relationshipLine",!0).attr("d",u(f.points)).style("stroke",k.stroke).style("fill","none");r.relSpec.relType===n.db.Identification.NON_IDENTIFYING&&c.attr("stroke-dasharray","8,8");let y="";switch(k.arrowMarkerAbsolute&&(y=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,y=y.replace(/\(/g,"\\("),y=y.replace(/\)/g,"\\)")),r.relSpec.cardA){case n.db.Cardinality.ZERO_OR_ONE:c.attr("marker-end","url("+y+"#"+B.ERMarkers.ZERO_OR_ONE_END+")");break;case n.db.Cardinality.ZERO_OR_MORE:c.attr("marker-end","url("+y+"#"+B.ERMarkers.ZERO_OR_MORE_END+")");break;case n.db.Cardinality.ONE_OR_MORE:c.attr("marker-end","url("+y+"#"+B.ERMarkers.ONE_OR_MORE_END+")");break;case n.db.Cardinality.ONLY_ONE:c.attr("marker-end","url("+y+"#"+B.ERMarkers.ONLY_ONE_END+")");break;case n.db.Cardinality.MD_PARENT:c.attr("marker-end","url("+y+"#"+B.ERMarkers.MD_PARENT_END+")")}switch(r.relSpec.cardB){case n.db.Cardinality.ZERO_OR_ONE:c.attr("marker-start","url("+y+"#"+B.ERMarkers.ZERO_OR_ONE_START+")");break;case n.db.Cardinality.ZERO_OR_MORE:c.attr("marker-start","url("+y+"#"+B.ERMarkers.ZERO_OR_MORE_START+")");break;case n.db.Cardinality.ONE_OR_MORE:c.attr("marker-start","url("+y+"#"+B.ERMarkers.ONE_OR_MORE_START+")");break;case n.db.Cardinality.ONLY_ONE:c.attr("marker-start","url("+y+"#"+B.ERMarkers.ONLY_ONE_START+")");break;case n.db.Cardinality.MD_PARENT:c.attr("marker-start","url("+y+"#"+B.ERMarkers.MD_PARENT_START+")")}const b=c.node().getTotalLength(),s=c.node().getPointAtLength(.5*b),_="rel"+ht,O=r.roleA.split(/<br ?\/>/g),E=t.append("text").classed("er relationshipLabel",!0).attr("id",_).attr("x",s.x).attr("y",s.y).style("text-anchor","middle").style("dominant-baseline","middle").style("font-family",F().fontFamily).style("font-size",k.fontSize+"px");if(O.length==1)E.text(r.roleA);else{const w=.5*-(O.length-1);O.forEach((C,P)=>{E.append("tspan").attr("x",s.x).attr("dy",`${P===0?w:1}em`).text(C)})}const T=E.node().getBBox();t.insert("rect","#"+_).classed("er relationshipLabelBox",!0).attr("x",s.x-T.width/2).attr("y",s.y-T.height/2).attr("width",T.width).attr("height",T.height)},"drawRelationshipFromLayout"),Qt=d(function(t,r,e,p){k=F().er,H.info("Drawing ER diagram");const n=F().securityLevel;let f;n==="sandbox"&&(f=ct("#i"+r));const u=ct(n==="sandbox"?f.nodes()[0].contentDocument.body:"body").select(`[id='${r}']`);let c;B.insertMarkers(u,k),c=new Mt({multigraph:!0,directed:!0,compound:!1}).setGraph({rankdir:k.layoutDirection,marginx:20,marginy:20,nodesep:100,edgesep:100,ranksep:100}).setDefaultEdgeLabel(function(){return{}});const y=Ut(u,p.db.getEntities(),c),b=Gt(p.db.getRelationships(),c);wt(c),Kt(u,c),b.forEach(function(T){Ht(u,T,c,y,p)});const s=k.diagramPadding;kt.insertTitle(u,"entityTitleText",k.titleTopMargin,p.db.getDiagramTitle());const _=u.node().getBBox(),O=_.width+2*s,E=_.height+2*s;Rt(u,E,O,k.useMaxWidth),u.attr("viewBox",`${_.x-s} ${_.y-s} ${O} ${E}`)},"draw"),jt="28e9f9db-3c8d-5aa5-9faf-44286ae5937c";function ut(t="",r=""){const e=t.replace(Ft,"");return`${rt(r)}${rt(e)}${vt(t,jt)}`}function rt(t=""){return t.length>0?`${t}-`:""}d(ut,"generateId"),d(rt,"strWithHyphen");var ze={parser:Lt,db:Zt,renderer:{setConf:zt,draw:Qt},styles:d(t=>`
  .entityBox {
    fill: ${t.mainBkg};
    stroke: ${t.nodeBorder};
  }

  .attributeBoxOdd {
    fill: ${t.attributeBackgroundColorOdd};
    stroke: ${t.nodeBorder};
  }

  .attributeBoxEven {
    fill:  ${t.attributeBackgroundColorEven};
    stroke: ${t.nodeBorder};
  }

  .relationshipLabelBox {
    fill: ${t.tertiaryColor};
    opacity: 0.7;
    background-color: ${t.tertiaryColor};
      rect {
        opacity: 0.5;
      }
  }

    .relationshipLine {
      stroke: ${t.lineColor};
    }

  .entityTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.textColor};
  }    
  #MD_PARENT_START {
    fill: #f5f5f5 !important;
    stroke: ${t.lineColor} !important;
    stroke-width: 1;
  }
  #MD_PARENT_END {
    fill: #f5f5f5 !important;
    stroke: ${t.lineColor} !important;
    stroke-width: 1;
  }
  
`,"getStyles")};export{ze as diagram};

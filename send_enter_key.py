#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接发送Enter键到当前活动窗口
基于Augment源代码分析的按键处理机制
"""

import time
import ctypes
from ctypes import wintypes
import sys

# Windows API常量
VK_RETURN = 0x0D
KEYEVENTF_KEYUP = 0x0002

def send_enter_key():
    """发送Enter键到当前活动窗口"""
    
    print("🎯 发送Enter键到Augment输入框")
    print("=" * 50)
    
    # 基于源代码分析：
    # keypress-DD1aQVr0.js: function r(t,o){return e=>!e.shiftKey&&e.key===t&&(o(e),!0)}
    print("📋 源代码分析：")
    print("- 检查条件: !e.shiftKey && e.key === 'Enter'")
    print("- 如果满足条件，执行回调函数发送消息")
    
    try:
        # 获取Windows API函数
        user32 = ctypes.windll.user32
        kernel32 = ctypes.windll.kernel32
        
        # 获取当前活动窗口
        hwnd = user32.GetForegroundWindow()
        if hwnd:
            # 获取窗口标题
            length = user32.GetWindowTextLengthW(hwnd)
            buff = ctypes.create_unicode_buffer(length + 1)
            user32.GetWindowTextW(hwnd, buff, length + 1)
            window_title = buff.value
            
            print(f"🎯 当前活动窗口: {window_title}")
            
            # 检查是否是IntelliJ IDEA或相关窗口
            if any(keyword in window_title.lower() for keyword in ['intellij', 'idea', 'augment', 'jetbrains']):
                print("✅ 检测到IntelliJ IDEA窗口")
            else:
                print(f"⚠️ 当前窗口可能不是IntelliJ IDEA: {window_title}")
                print("请确保Augment聊天界面已获得焦点")
        
        print("\n⏰ 3秒后发送Enter键...")
        print("请确保：")
        print("1. Augment聊天界面已打开")
        print("2. 输入框已获得焦点")
        print("3. 输入框中有要发送的消息")
        
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        print("\n📤 发送Enter键...")
        
        # 发送Enter键按下事件
        user32.keybd_event(VK_RETURN, 0, 0, 0)
        time.sleep(0.05)  # 短暂延迟
        
        # 发送Enter键释放事件
        user32.keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0)
        
        print("✅ Enter键已发送！")
        
        return True
        
    except Exception as e:
        print(f"❌ 发送Enter键失败: {e}")
        return False

def find_augment_window():
    """查找Augment相关的窗口"""
    
    print("🔍 查找Augment相关窗口...")
    
    try:
        user32 = ctypes.windll.user32
        
        # 枚举所有窗口的回调函数
        def enum_windows_proc(hwnd, lParam):
            if user32.IsWindowVisible(hwnd):
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buff = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buff, length + 1)
                    window_title = buff.value
                    
                    # 检查是否包含相关关键词
                    keywords = ['intellij', 'idea', 'augment', 'jetbrains']
                    if any(keyword in window_title.lower() for keyword in keywords):
                        print(f"✅ 找到相关窗口: {window_title}")
                        
                        # 激活这个窗口
                        user32.SetForegroundWindow(hwnd)
                        user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                        
                        return False  # 停止枚举
            return True
        
        # 定义回调函数类型
        EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
        
        # 枚举所有窗口
        user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
        
    except Exception as e:
        print(f"❌ 查找窗口失败: {e}")

def main():
    print("🎯 Augment Enter键发送工具")
    print("基于源代码分析的按键处理机制")
    print("=" * 60)
    
    # 查找并激活Augment窗口
    find_augment_window()
    
    time.sleep(1)  # 等待窗口激活
    
    # 发送Enter键
    success = send_enter_key()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 Enter键发送完成！")
        print("\n📋 如果消息没有发送，可能的原因：")
        print("1. 输入框没有获得焦点")
        print("2. 输入框中没有内容")
        print("3. Augment的按键处理被其他事件拦截")
        print("4. 需要先在输入框中输入消息内容")
        
        print("\n💡 建议操作：")
        print("1. 手动点击Augment的输入框")
        print("2. 输入要发送的消息")
        print("3. 再次运行此脚本")
    else:
        print("❌ Enter键发送失败！")

if __name__ == "__main__":
    main()

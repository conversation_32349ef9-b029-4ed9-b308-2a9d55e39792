#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment AI 真正的API调用
基于源代码分析的完整实现
"""

import json
import subprocess
import time
import os
import sys

def send_message_to_augment_real(message):
    """真正发送消息到Augment AI"""
    
    print(f"📤 真正发送消息: {message}")
    print("=" * 60)
    
    # 1. 查找sidecar路径
    sidecar_path = None
    for root, dirs, files in os.walk('.'):
        if 'sidecar' in root and 'index.js' in files:
            sidecar_path = os.path.join(root, 'index.js')
            break
    
    if not sidecar_path:
        print("❌ 未找到sidecar/index.js")
        return False
    
    print(f"✅ 找到sidecar: {sidecar_path}")
    
    try:
        # 2. 启动LSP服务器
        print("🚀 启动LSP服务器...")
        lsp_process = subprocess.Popen(
            ['node', sidecar_path, '--stdio'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.path.dirname(sidecar_path)
        )
        
        # 3. LSP消息构建函数
        def create_lsp_message(method, params, msg_id=None):
            if msg_id is None:
                msg_id = int(time.time() * 1000)
            
            request = {
                "jsonrpc": "2.0",
                "id": msg_id,
                "method": method,
                "params": params
            }
            request_json = json.dumps(request, ensure_ascii=False)
            content_length = len(request_json.encode('utf-8'))
            return f"Content-Length: {content_length}\r\n\r\n{request_json}"
        
        # 4. 发送正确的初始化请求 - 基于InitializeParams proto定义
        print("📤 发送初始化请求...")
        init_params = {
            "process_id": os.getpid(),
            "capabilities": {
                "featureFlags": {
                    "agentEditTool": "str_replace_editor_tool",
                    "chatMode": "CHAT"
                }
            }
        }
        
        init_message = create_lsp_message("initialize", init_params)
        print(f"📝 初始化消息: {init_message}")
        
        lsp_process.stdin.write(init_message)
        lsp_process.stdin.flush()
        
        # 5. 等待初始化完成
        time.sleep(2)
        
        # 6. 发送initialized通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "initialized",
            "params": {}
        }
        initialized_json = json.dumps(initialized_notification)
        initialized_length = len(initialized_json.encode('utf-8'))
        initialized_message = f"Content-Length: {initialized_length}\r\n\r\n{initialized_json}"
        
        print("📤 发送initialized通知...")
        lsp_process.stdin.write(initialized_message)
        lsp_process.stdin.flush()
        
        # 7. 等待服务器完全初始化
        time.sleep(2)
        
        # 8. 构建WebView消息 - 基于源代码分析
        webview_message = {
            "type": "chat-user-message",
            "data": {
                "text": message,
                "chatHistory": [],
                "modelId": "default",
                "disableRetrieval": False,
                "disableSelectedCodeDetails": False,
                "silent": False
            }
        }
        
        # 9. 包装为ProcessWebviewMessageRequest格式
        webview_params = {
            "message": json.dumps(webview_message, ensure_ascii=False)
        }
        
        # 10. 发送WebView消息
        webview_request = create_lsp_message("augmentcode/webview-message", webview_params)
        
        print("📤 发送WebView消息...")
        print(f"📝 WebView消息: {webview_request}")
        
        lsp_process.stdin.write(webview_request)
        lsp_process.stdin.flush()
        
        # 11. 等待响应
        print("⏳ 等待响应...")
        time.sleep(5)
        
        # 12. 读取响应
        try:
            # 尝试读取stdout
            lsp_process.stdin.close()
            stdout, stderr = lsp_process.communicate(timeout=5)
            
            if stdout:
                print(f"✅ 收到响应: {stdout}")
            if stderr:
                print(f"⚠️ 错误输出: {stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ 响应超时")
            lsp_process.kill()
        
        print("✅ 消息处理完成！")
        return True
        
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        if 'lsp_process' in locals():
            lsp_process.kill()
        return False

def main():
    if len(sys.argv) > 1:
        message = " ".join(sys.argv[1:])
    else:
        message = "你好，Augment AI！这是通过真正的LSP协议发送的消息。请在聊天界面回复确认收到！"
    
    print("🎯 Augment AI 真正的消息发送工具")
    print("基于完整源代码分析的LSP实现")
    print("=" * 60)
    
    success = send_message_to_augment_real(message)
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 消息发送完成！")
        print("请检查Augment聊天界面是否收到消息。")
        print("\n💡 如果没有收到消息，可能的原因：")
        print("1. WebviewMessaging客户端未正确初始化")
        print("2. 需要与IntelliJ IDEA的现有LSP连接交互")
        print("3. 消息需要通过特定的认证或会话")
    else:
        print("❌ 消息发送失败！")

if __name__ == "__main__":
    main()

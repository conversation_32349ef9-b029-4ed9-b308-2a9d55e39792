#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接发送消息到Augment - 基于204状态码的发现
"""

import requests
import json

def send_message_to_augment(message):
    """直接发送消息到Augment AI"""
    
    base_url = "http://localhost:63342"
    
    # 我们知道 /api/augment 返回204，说明端点存在
    # 尝试不同的HTTP方法和消息格式
    
    print(f"📤 发送消息: {message}")
    print("=" * 50)
    
    # 消息格式1: 简单JSON
    payload1 = {"message": message}
    
    # 消息格式2: 聊天格式
    payload2 = {
        "text": message,
        "type": "chat"
    }
    
    # 消息格式3: WebView格式
    payload3 = {
        "type": "chat-user-message",
        "data": {
            "text": message
        }
    }
    
    # 消息格式4: 完整格式
    payload4 = {
        "text": message,
        "chatHistory": [],
        "modelId": "default",
        "disableRetrieval": False
    }
    
    payloads = [
        ("简单JSON", payload1),
        ("聊天格式", payload2), 
        ("WebView格式", payload3),
        ("完整格式", payload4)
    ]
    
    # 尝试不同的HTTP方法
    methods = ["POST", "PUT", "PATCH"]
    
    for method in methods:
        print(f"\n🔧 尝试 {method} 方法:")
        
        for name, payload in payloads:
            try:
                print(f"  📝 {name}: ", end="")
                
                response = requests.request(
                    method,
                    f"{base_url}/api/augment",
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    },
                    timeout=10
                )
                
                print(f"{response.status_code}", end="")
                
                if response.status_code in [200, 201, 202, 204]:
                    print(" ✅ 成功!")
                    if response.text:
                        print(f"    响应: {response.text}")
                    return True
                else:
                    print(f" - {response.text[:50] if response.text else 'No content'}")
                    
            except Exception as e:
                print(f" ❌ 错误: {e}")
    
    # 尝试其他可能的端点
    print(f"\n🔍 尝试其他端点:")
    
    other_endpoints = [
        "/api/augment/send",
        "/api/augment/message", 
        "/api/augment/chat",
        "/api/augment/query"
    ]
    
    for endpoint in other_endpoints:
        try:
            print(f"  📍 {endpoint}: ", end="")
            
            response = requests.post(
                f"{base_url}{endpoint}",
                json=payload4,  # 使用完整格式
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            
            print(f"{response.status_code}", end="")
            
            if response.status_code in [200, 201, 202, 204]:
                print(" ✅ 成功!")
                if response.text:
                    print(f"    响应: {response.text}")
                return True
            else:
                print()
                
        except Exception as e:
            print(f" ❌ {e}")
    
    return False

def main():
    message = "你好，Augment AI！这是一条测试消息，请回复确认收到。"
    
    print("🤖 直接发送消息到Augment AI")
    print("基于发现的 /api/augment 端点（204状态码）")
    print("=" * 60)
    
    success = send_message_to_augment(message)
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 消息发送成功！请检查Augment聊天界面是否收到消息。")
    else:
        print("❌ 消息发送失败。")
        print("\n💡 可能的原因：")
        print("1. Augment插件需要特定的认证")
        print("2. 消息格式不正确")
        print("3. 需要通过不同的协议（如WebSocket）")
        print("4. 插件可能使用内部通信机制")

if __name__ == "__main__":
    main()

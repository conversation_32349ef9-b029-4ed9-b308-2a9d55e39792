import{S as Y,i as B,s as K,W as H,D as O,t as u,q as d,E as P,G as Q,I as T,d as _,c as b,N as U,a8 as N,a as Z,o as F,p as q,X as L,a9 as D,g as aa,e as w,R as na,aa as ea,F as y,V as k,Y as M,h as z,J as R,K as V,L as W,M as X,a1 as S}from"./SpinnerAugment-BejxPZX4.js";import{n as ta,g as ca,a as la}from"./file-paths-B4wu8Zer.js";const oa=e=>({}),j=e=>({}),ia=e=>({}),A=e=>({});function E(e){let a;const l=e[11].leftIcon,c=R(l,e,e[12],A);return{c(){c&&c.c()},m(n,t){c&&c.m(n,t),a=!0},p(n,t){c&&c.p&&(!a||4096&t)&&V(c,l,n,n[12],a?X(l,n[12],t,ia):W(n[12]),A)},i(n){a||(d(c,n),a=!0)},o(n){u(c,n),a=!1},d(n){c&&c.d(n)}}}function G(e){let a,l,c;return{c(){a=y("div"),l=y("div"),c=M(e[6]),z(l,"class","c-filespan__dir-text svelte-9pfhnp"),z(a,"class","c-filespan__dir svelte-9pfhnp"),S(a,"growname",e[3])},m(n,t){b(n,a,t),w(a,l),w(l,c)},p(n,t){64&t&&L(c,n[6]),8&t&&S(a,"growname",n[3])},d(n){n&&_(a)}}}function J(e){let a,l;const c=e[11].rightIcon,n=R(c,e,e[12],j);return{c(){a=y("span"),n&&n.c(),z(a,"class","right-icons svelte-9pfhnp")},m(t,s){b(t,a,s),n&&n.m(a,null),l=!0},p(t,s){n&&n.p&&(!l||4096&s)&&V(n,c,t,t[12],l?X(c,t[12],s,oa):W(t[12]),j)},i(t){l||(d(n,t),l=!0)},o(t){u(n,t),l=!1},d(t){t&&_(a),n&&n.d(t)}}}function C(e){let a,l,c,n,t,s,h,g,m,x,I,o=e[8].leftIcon&&E(e),r=!e[2]&&G(e),i=e[8].rightIcon&&J(e),v=[{class:h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"},{role:g=e[4]?"button":""},{tabindex:"0"}],p={};for(let $=0;$<v.length;$+=1)p=Z(p,v[$]);return{c(){a=y(e[5]),o&&o.c(),l=k(),c=y("span"),n=M(e[7]),t=k(),r&&r.c(),s=k(),i&&i.c(),z(c,"class","c-filespan__filename svelte-9pfhnp"),D(e[5])(a,p)},m($,f){b($,a,f),o&&o.m(a,null),w(a,l),w(a,c),w(c,n),w(a,t),r&&r.m(a,null),w(a,s),i&&i.m(a,null),m=!0,x||(I=na(a,"click",function(){ea(e[4])&&e[4].apply(this,arguments)}),x=!0)},p($,f){(e=$)[8].leftIcon?o?(o.p(e,f),256&f&&d(o,1)):(o=E(e),o.c(),d(o,1),o.m(a,l)):o&&(F(),u(o,1,1,()=>{o=null}),q()),(!m||128&f)&&L(n,e[7]),e[2]?r&&(r.d(1),r=null):r?r.p(e,f):(r=G(e),r.c(),r.m(a,s)),e[8].rightIcon?i?(i.p(e,f),256&f&&d(i,1)):(i=J(e),i.c(),d(i,1),i.m(a,null)):i&&(F(),u(i,1,1,()=>{i=null}),q()),D(e[5])(a,p=aa(v,[(!m||1&f&&h!==(h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"))&&{class:h},(!m||16&f&&g!==(g=e[4]?"button":""))&&{role:g},{tabindex:"0"}]))},i($){m||(d(o),d(i),m=!0)},o($){u(o),u(i),m=!1},d($){$&&_(a),o&&o.d(),r&&r.d(),i&&i.d(),x=!1,I()}}}function pa(e){let a,l,c=e[5],n=e[5]&&C(e);return{c(){n&&n.c(),a=U()},m(t,s){n&&n.m(t,s),b(t,a,s),l=!0},p(t,s){t[5]?c?K(c,t[5])?(n.d(1),n=C(t),c=t[5],n.c(),n.m(a.parentNode,a)):n.p(t,s):(n=C(t),c=t[5],n.c(),n.m(a.parentNode,a)):c&&(n.d(1),n=null,c=t[5])},i(t){l||(d(n,t),l=!0)},o(t){u(n,t),l=!1},d(t){t&&_(a),n&&n.d(t)}}}function ra(e){let a,l;return a=new H({props:{size:e[1],$$slots:{default:[pa]},$$scope:{ctx:e}}}),{c(){Q(a.$$.fragment)},m(c,n){P(a,c,n),l=!0},p(c,[n]){const t={};2&n&&(t.size=c[1]),4605&n&&(t.$$scope={dirty:n,ctx:c}),a.$set(t)},i(c){l||(d(a.$$.fragment,c),l=!0)},o(c){u(a.$$.fragment,c),l=!1},d(c){O(a,c)}}}function sa(e,a,l){let c,n,t,s,{$$slots:h={},$$scope:g}=a;const m=T(h);let{class:x=""}=a,{filepath:I}=a,{size:o=1}=a,{nopath:r=!1}=a,{growname:i=!0}=a,{onClick:v}=a;return e.$$set=p=>{"class"in p&&l(0,x=p.class),"filepath"in p&&l(9,I=p.filepath),"size"in p&&l(1,o=p.size),"nopath"in p&&l(2,r=p.nopath),"growname"in p&&l(3,i=p.growname),"onClick"in p&&l(4,v=p.onClick),"$$scope"in p&&l(12,g=p.$$scope)},e.$$.update=()=>{512&e.$$.dirty&&l(10,c=ta(I)),1024&e.$$.dirty&&l(7,n=ca(c)),1024&e.$$.dirty&&l(6,t=la(c)),16&e.$$.dirty&&l(5,s=v?"button":"div")},[x,o,r,i,v,s,t,n,m,I,c,h,g]}class da extends Y{constructor(a){super(),B(this,a,sa,ra,K,{class:0,filepath:9,size:1,nopath:2,growname:3,onClick:4})}}export{da as F};

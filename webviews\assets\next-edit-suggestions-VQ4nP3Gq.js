const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-IW1pin9L.css","./NextEditSuggestions-D3NIrm22.js","./SpinnerAugment-BejxPZX4.js","./SpinnerAugment-VPe0cp57.css","./IconButtonAugment-DmfIVAYG.js","./IconButtonAugment-FeoFGSYm.css","./next-edit-types-904A5ehg.js","./IconFilePath-BUMTFdYT.js","./LanguageIcon-B9tL-FlO.js","./LanguageIcon-D78BqCXT.css","./IconFilePath-CiKel2Kp.css","./async-messaging-D7f6isRQ.js","./Drawer-Bum6OuhB.js","./index-Dkoz80k5.js","./ellipsis-BRv5sK55.js","./Drawer-DwFbLE28.css","./keypress-DD1aQVr0.js","./VSCodeCodicon-ChMRIfwW.js","./VSCodeCodicon-DVaocTud.css","./monaco-render-utils-DfwV7QLY.js","./toggleHighContrast-Cb9MCs64.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./index-6AlVWKh5.js","./index-BlaqVU85.js","./index-McRKs1sU.css","./ButtonAugment-CVatBELe.js","./ButtonAugment-zn72hvQy.css","./NextEditSuggestions-DCdO-yAa.css"])))=>i.map(i=>d[i]);
import{S as N,i as W,s as B,d as b,t as f,q as h,c as x,N as H,af as K,ah as U,W as A,D as v,ai as T,aj as R,e as y,E as w,F as M,G as k,V as I,h as P,a as X,g as J,ae as Y,an as Q,X as D,Y as S,n as Z}from"./SpinnerAugment-BejxPZX4.js";import"./design-system-init-Cz4_d9qQ.js";import{_ as F}from"./preload-helper-Dv6uf1Os.js";import{h as ee,u as te}from"./await_block-Dt1rTGTc.js";import{A as V}from"./augment-logo-BRK9m6wM.js";import{a as z}from"./index-Dkoz80k5.js";import{M as ne}from"./index-6AlVWKh5.js";import"./index-BlaqVU85.js";const G={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};function re(s){let e,n,t,a,o,$,i,g,p,u,c,d;return t=new V({}),o=new A({props:{size:3,$$slots:{default:[ae]},$$scope:{ctx:s}}}),g=new A({props:{size:3,$$slots:{default:[oe]},$$scope:{ctx:s}}}),u=new A({props:{size:1,$$slots:{default:[ie]},$$scope:{ctx:s}}}),{c(){e=M("div"),n=M("div"),k(t.$$.fragment),a=I(),k(o.$$.fragment),$=I(),i=M("div"),k(g.$$.fragment),p=I(),k(u.$$.fragment),P(n,"class","l-loader__logo svelte-a8cfrv"),P(i,"class","l-loader__message-container l-loader-error-message svelte-a8cfrv"),P(e,"class","l-loader svelte-a8cfrv")},m(r,m){x(r,e,m),y(e,n),w(t,n,null),y(n,a),w(o,n,null),y(e,$),y(e,i),w(g,i,null),y(i,p),w(u,i,null),d=!0},p(r,m){const _={};131074&m&&(_.$$scope={dirty:m,ctx:r}),o.$set(_);const l={};131072&m&&(l.$$scope={dirty:m,ctx:r}),g.$set(l);const C={};131076&m&&(C.$$scope={dirty:m,ctx:r}),u.$set(C)},i(r){d||(h(t.$$.fragment,r),h(o.$$.fragment,r),h(g.$$.fragment,r),h(u.$$.fragment,r),r&&R(()=>{d&&(c||(c=T(e,z,{},!0)),c.run(1))}),d=!0)},o(r){f(t.$$.fragment,r),f(o.$$.fragment,r),f(g.$$.fragment,r),f(u.$$.fragment,r),r&&(c||(c=T(e,z,{},!1)),c.run(0)),d=!1},d(r){r&&b(e),v(t),v(o),v(g),v(u),r&&c&&c.end()}}}function ae(s){let e;return{c(){e=S(s[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&D(e,n[1])},d(n){n&&b(e)}}}function oe(s){let e;return{c(){e=S("An Error Occurred.")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function ie(s){let e,n;return{c(){e=M("code"),n=S(s[2])},m(t,a){x(t,e,a),y(e,n)},p(t,a){4&a&&D(n,t[2])},d(t){t&&b(e)}}}function se(s){let e,n,t,a;const o=[s[0]];let $={};for(let i=0;i<o.length;i+=1)$=X($,o[i]);return n=new s[15]({props:$}),{c(){e=M("div"),k(n.$$.fragment),P(e,"class","l-component svelte-a8cfrv")},m(i,g){x(i,e,g),w(n,e,null),a=!0},p(i,g){const p=1&g?J(o,[Y(i[0])]):{};n.$set(p)},i(i){a||(h(n.$$.fragment,i),i&&R(()=>{a&&(t||(t=T(e,z,{},!0)),t.run(1))}),a=!0)},o(i){f(n.$$.fragment,i),i&&(t||(t=T(e,z,{},!1)),t.run(0)),a=!1},d(i){i&&b(e),v(n),i&&t&&t.end()}}}function le(s){let e,n,t,a,o,$,i,g,p,u,c,d;return t=new V({}),o=new A({props:{size:2,$$slots:{default:[ge]},$$scope:{ctx:s}}}),g=new Q({}),u=new A({props:{size:1,color:"secondary",$$slots:{default:[ce]},$$scope:{ctx:s}}}),{c(){e=M("div"),n=M("div"),k(t.$$.fragment),a=I(),k(o.$$.fragment),$=I(),i=M("div"),k(g.$$.fragment),p=I(),k(u.$$.fragment),P(n,"class","l-loader__logo svelte-a8cfrv"),P(i,"class","l-loader__message-container svelte-a8cfrv"),P(e,"class","l-loader svelte-a8cfrv")},m(r,m){x(r,e,m),y(e,n),w(t,n,null),y(n,a),w(o,n,null),y(e,$),y(e,i),w(g,i,null),y(i,p),w(u,i,null),d=!0},p(r,m){const _={};131074&m&&(_.$$scope={dirty:m,ctx:r}),o.$set(_);const l={};131080&m&&(l.$$scope={dirty:m,ctx:r}),u.$set(l)},i(r){d||(h(t.$$.fragment,r),h(o.$$.fragment,r),h(g.$$.fragment,r),h(u.$$.fragment,r),r&&R(()=>{d&&(c||(c=T(e,z,{},!0)),c.run(1))}),d=!0)},o(r){f(t.$$.fragment,r),f(o.$$.fragment,r),f(g.$$.fragment,r),f(u.$$.fragment,r),r&&(c||(c=T(e,z,{},!1)),c.run(0)),d=!1},d(r){r&&b(e),v(t),v(o),v(g),v(u),r&&c&&c.end()}}}function ge(s){let e;return{c(){e=S(s[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&D(e,n[1])},d(n){n&&b(e)}}}function ce(s){let e;return{c(){e=S(s[3])},m(n,t){x(n,e,t)},p(n,t){8&t&&D(e,n[3])},d(n){n&&b(e)}}}function me(s){let e,n,t={ctx:s,current:null,token:null,hasCatch:!0,pending:le,then:se,catch:re,value:15,error:16,blocks:[,,,]};return ee(s[4](),t),{c(){e=H(),t.block.c()},m(a,o){x(a,e,o),t.block.m(a,t.anchor=o),t.mount=()=>e.parentNode,t.anchor=e,n=!0},p(a,[o]){te(t,s=a,o)},i(a){n||(h(t.block),n=!0)},o(a){for(let o=0;o<3;o+=1){const $=t.blocks[o];f($)}n=!1},d(a){a&&b(e),t.block.d(a),t.token=null,t=null}}}function ue(s,e,n){let{minDisplayTime:t=1e3}=e,{loader:a}=e,{props:o}=e,{title:$="Augment Code"}=e,{randomize:i=!0}=e,{retryCount:g=3}=e,{loadingMessages:p=G.messages}=e,{errorMessages:u=G.errors}=e,{errorMessage:c="An error occurred while loading. Please try again later."}=e,d=p.slice(1),r=p[0],m="loading",_=new AbortController;return K(()=>_.abort()),U(async function(){d.length===0&&(d=[...m==="retry"?u:p]),n(3,r=m==="error"?c:d.splice(m!=="retry"&&i?Math.floor(Math.random()*d.length):0,1)[0]??"")}),s.$$set=l=>{"minDisplayTime"in l&&n(5,t=l.minDisplayTime),"loader"in l&&n(6,a=l.loader),"props"in l&&n(0,o=l.props),"title"in l&&n(1,$=l.title),"randomize"in l&&n(7,i=l.randomize),"retryCount"in l&&n(8,g=l.retryCount),"loadingMessages"in l&&n(9,p=l.loadingMessages),"errorMessages"in l&&n(10,u=l.errorMessages),"errorMessage"in l&&n(2,c=l.errorMessage)},[o,$,c,r,async function l(C=0){try{const[E]=await Promise.all([a(),(O=t,L=_.signal,new Promise(q=>{const j=setTimeout(q,O);L&&L.addEventListener("abort",()=>{clearTimeout(j),q()})}))]);return E}catch(E){if(console.error("Failed to load component",E),m="retry",C===0&&(d=[...u]),g&&C<=g)return await l(C+1);throw m="error",new Error("Failed to load component after retrying. Please try again later.")}var O,L},t,a,i,g,p,u]}class de extends N{constructor(e){super(),W(this,e,ue,me,B,{minDisplayTime:5,loader:6,props:0,title:1,randomize:7,retryCount:8,loadingMessages:9,errorMessages:10,errorMessage:2})}}function $e(s){let e,n;return e=new de({props:{loader:s[0],props:{}}}),{c(){k(e.$$.fragment)},m(t,a){w(e,t,a),n=!0},p:Z,i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function pe(s){let e,n;return e=new ne.Root({props:{$$slots:{default:[$e]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment)},m(t,a){w(e,t,a),n=!0},p(t,[a]){const o={};2&a&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function fe(s){return[async()=>(await F(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await F(async()=>{const{default:e}=await import("./NextEditSuggestions-D3NIrm22.js");return{default:e}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28]),import.meta.url)).default)]}new class extends N{constructor(s){super(),W(this,s,fe,pe,B,{})}}({target:document.getElementById("app")});

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Augment连接测试脚本
"""

import requests
import json

def test_basic_connection():
    """测试基本连接"""
    base_url = "http://localhost:63342"
    
    print("🔍 测试IntelliJ IDEA HTTP API连接...")
    
    # 测试基本端点
    test_endpoints = [
        "/api/about",
        "/api",
        "/",
        "/api/file",
        "/api/projects"
    ]
    
    for endpoint in test_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"✅ {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应: {response.text[:100]}...")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    print("\n" + "="*50)
    
    # 尝试发现Augment相关端点
    print("🔍 尝试发现Augment相关端点...")
    
    augment_endpoints = [
        "/api/augment",
        "/augment",
        "/api/plugins/augment",
        "/api/webview",
        "/api/messaging",
        "/api/chat",
        "/api/service",
        "/api/rpc"
    ]
    
    for endpoint in augment_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"✅ {endpoint}: {response.status_code}")
            if response.status_code != 404:
                print(f"   响应: {response.text[:200]}...")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def test_post_endpoints():
    """测试POST端点"""
    base_url = "http://localhost:63342"
    
    print("\n" + "="*50)
    print("🔍 测试POST端点...")
    
    test_payload = {
        "text": "Hello",
        "type": "test"
    }
    
    post_endpoints = [
        "/api/augment/chat",
        "/api/chat",
        "/api/webview/message",
        "/api/messaging",
        "/api/service/call"
    ]
    
    for endpoint in post_endpoints:
        try:
            response = requests.post(
                f"{base_url}{endpoint}", 
                json=test_payload, 
                timeout=5,
                headers={"Content-Type": "application/json"}
            )
            print(f"✅ POST {endpoint}: {response.status_code}")
            if response.status_code != 404:
                print(f"   响应: {response.text[:200]}...")
        except Exception as e:
            print(f"❌ POST {endpoint}: {e}")

def main():
    print("🤖 Augment连接测试工具")
    print("="*50)
    
    test_basic_connection()
    test_post_endpoints()
    
    print("\n" + "="*50)
    print("📝 测试完成！")
    print("\n💡 提示：")
    print("  - 如果看到200状态码，说明端点可用")
    print("  - 404表示端点不存在")
    print("  - 其他状态码可能表示需要认证或其他问题")

if __name__ == "__main__":
    main()

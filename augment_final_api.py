#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment AI 最终API调用脚本
基于源代码分析的确切实现 - 绕过UI界面直接发送消息
"""

import requests
import json
import time
import subprocess
import platform

class AugmentFinalAPI:
    def __init__(self):
        self.base_url = "http://localhost:63342"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'IntelliJ-API-Client'
        })
    
    def send_message_via_intellij_api(self, text: str) -> dict:
        """
        通过IntelliJ IDEA内置API发送消息
        
        基于plugin.xml中的httpRequestHandler实现：
        <httpRequestHandler implementation="com.augmentcode.intellij.auth.AugmentOAuthCallbackHandler" />
        """
        
        print(f"📤 通过IntelliJ API发送: {text[:50]}...")
        
        # 方法1: 直接调用Augment HTTP处理器
        endpoints = [
            "/api/augment",
            "/augment/api",
            "/augment/chat",
            "/augment/message"
        ]
        
        # 构建消息格式 - 基于ChatUserMessageData
        payload = {
            "text": text,
            "chatHistory": [],
            "modelId": "default",
            "disableRetrieval": False,
            "silent": False
        }
        
        for endpoint in endpoints:
            try:
                print(f"🎯 尝试端点: {endpoint}")
                
                # 尝试POST请求
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=payload,
                    timeout=10
                )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 消息发送成功！")
                    return response.json()
                elif response.status_code == 204:
                    print("✅ 消息已接收（无内容返回）")
                    return {"status": "accepted", "message": "Message received"}
                elif response.status_code != 404:
                    print(f"   响应: {response.text[:200]}...")
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        return None
    
    def send_message_via_webview_protocol(self, text: str) -> dict:
        """
        使用WebView协议发送消息
        
        基于sidecar/index.js中的webview-message处理
        """
        
        print(f"📨 通过WebView协议发送: {text[:50]}...")
        
        # 构建WebView消息格式
        webview_message = {
            "type": "chat-user-message",
            "data": {
                "text": text,
                "chatHistory": [],
                "modelId": "default",
                "disableRetrieval": False,
                "disableSelectedCodeDetails": False,
                "silent": False
            }
        }
        
        # 包装为ProcessWebviewMessageRequest格式
        request_payload = {
            "message": json.dumps(webview_message)
        }
        
        # 尝试不同的WebView端点
        webview_endpoints = [
            "/api/webview",
            "/api/webview/message", 
            "/api/messaging",
            "/api/lsp/webview",
            "/webview/api"
        ]
        
        for endpoint in webview_endpoints:
            try:
                print(f"🎯 尝试WebView端点: {endpoint}")
                
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=request_payload,
                    timeout=10
                )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ WebView消息发送成功！")
                    return response.json()
                elif response.status_code == 204:
                    print("✅ WebView消息已接收")
                    return {"status": "accepted", "message": "WebView message received"}
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        return None
    
    def send_message_via_oauth_handler(self, text: str) -> dict:
        """
        通过OAuth回调处理器发送消息
        
        基于AugmentOAuthCallbackHandler的实现
        """
        
        print(f"🔐 通过OAuth处理器发送: {text[:50]}...")
        
        # OAuth回调格式的消息
        oauth_payload = {
            "action": "chat",
            "message": text,
            "source": "api",
            "timestamp": int(time.time() * 1000)
        }
        
        # OAuth处理器可能的端点
        oauth_endpoints = [
            "/oauth/callback",
            "/auth/callback", 
            "/api/oauth",
            "/api/auth/callback"
        ]
        
        for endpoint in oauth_endpoints:
            try:
                print(f"🎯 尝试OAuth端点: {endpoint}")
                
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=oauth_payload,
                    timeout=10
                )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code in [200, 204]:
                    print("✅ OAuth消息发送成功！")
                    return response.json() if response.text else {"status": "success"}
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        return None
    
    def send_message_all_methods(self, text: str) -> dict:
        """
        尝试所有已知的API方法
        """
        
        print(f"🚀 发送消息: {text}")
        print("=" * 60)
        
        # 方法1: IntelliJ API
        result = self.send_message_via_intellij_api(text)
        if result:
            return result
        
        print("\n" + "-" * 40 + "\n")
        
        # 方法2: WebView协议
        result = self.send_message_via_webview_protocol(text)
        if result:
            return result
        
        print("\n" + "-" * 40 + "\n")
        
        # 方法3: OAuth处理器
        result = self.send_message_via_oauth_handler(text)
        if result:
            return result
        
        return None

def main():
    print("🎯 Augment AI 最终API调用工具")
    print("基于源代码分析 - 绕过UI界面直接发送消息")
    print("=" * 60)
    
    api = AugmentFinalAPI()
    
    # 测试消息
    test_message = "你好，Augment AI！请解释一下当前项目的结构和主要功能。"
    
    # 发送消息
    result = api.send_message_all_methods(test_message)
    
    print("\n" + "=" * 60)
    
    if result:
        print("🎉 消息发送成功！")
        print(f"📝 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        print("\n✅ 成功找到工作的API调用方式！")
        print("现在您可以使用这个方法绕过UI界面直接与Augment AI交互。")
        
    else:
        print("❌ 所有API方法都失败了")
        
        print("\n🔧 最终诊断：")
        print("1. 确认IntelliJ IDEA正在运行且Augment插件已加载")
        print("2. 确认插件已登录并处于活跃状态") 
        print("3. 检查是否有防火墙阻止本地连接")
        print("4. 尝试重启IDE和插件")
        
        print("\n📋 基于源代码分析的确切发现：")
        print("- HTTP请求处理器: AugmentOAuthCallbackHandler")
        print("- WebView消息协议: augmentcode/webview-message")
        print("- 消息格式: ChatUserMessageRequest with ChatUserMessageData")
        print("- 端点存在但可能需要特定的认证或会话状态")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接到现有的Augment LSP进程
基于发现的现有进程信息
"""

import psutil
import json
import time
import os
import sys

def find_augment_lsp_process():
    """查找现有的Augment LSP进程"""
    print("🔍 查找现有的Augment LSP进程...")
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'node' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('sidecar' in str(arg) for arg in cmdline):
                    print(f"✅ 找到Augment LSP进程:")
                    print(f"   PID: {proc.info['pid']}")
                    print(f"   命令行: {' '.join(cmdline)}")
                    
                    # 查看进程的文件描述符
                    try:
                        connections = proc.connections()
                        if connections:
                            print(f"   网络连接: {len(connections)} 个")
                            for conn in connections[:3]:  # 只显示前3个
                                print(f"     {conn}")
                    except (psutil.AccessDenied, psutil.NoSuchProcess):
                        pass
                    
                    return proc.info['pid'], cmdline
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print("❌ 未找到Augment LSP进程")
    return None, None

def analyze_lsp_process(pid):
    """分析LSP进程的通信方式"""
    print(f"🔍 分析LSP进程 {pid} 的通信方式...")
    
    try:
        proc = psutil.Process(pid)
        
        # 查看进程的文件描述符
        print("📁 文件描述符:")
        try:
            open_files = proc.open_files()
            for f in open_files[:10]:  # 只显示前10个
                print(f"   {f.path}")
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            print("   无法访问文件描述符")
        
        # 查看网络连接
        print("🌐 网络连接:")
        try:
            connections = proc.connections()
            for conn in connections:
                if conn.status == 'LISTEN':
                    print(f"   监听端口: {conn.laddr}")
                elif conn.status == 'ESTABLISHED':
                    print(f"   已建立连接: {conn.laddr} -> {conn.raddr}")
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            print("   无法访问网络连接")
        
        # 查看环境变量
        print("🔧 环境变量:")
        try:
            environ = proc.environ()
            for key in ['PATH', 'NODE_ENV', 'AUGMENT_']:
                if key in environ:
                    print(f"   {key}: {environ[key][:100]}...")
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            print("   无法访问环境变量")
            
    except psutil.NoSuchProcess:
        print(f"❌ 进程 {pid} 不存在")

def try_communicate_with_existing_process(pid):
    """尝试与现有进程通信"""
    print(f"📡 尝试与现有LSP进程 {pid} 通信...")
    
    try:
        proc = psutil.Process(pid)
        
        # 方法1: 查找监听端口
        connections = proc.connections()
        listening_ports = [conn.laddr.port for conn in connections if conn.status == 'LISTEN']
        
        if listening_ports:
            print(f"✅ 发现监听端口: {listening_ports}")
            
            # 尝试连接到监听端口
            import socket
            for port in listening_ports:
                try:
                    print(f"🔌 尝试连接端口 {port}...")
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex(('localhost', port))
                    
                    if result == 0:
                        print(f"✅ 成功连接到端口 {port}")
                        
                        # 尝试发送LSP消息
                        message = {
                            "jsonrpc": "2.0",
                            "id": 1,
                            "method": "augmentcode/webview-message",
                            "params": {
                                "message": json.dumps({
                                    "type": "chat-user-message",
                                    "data": {
                                        "text": "测试消息：通过端口连接发送",
                                        "chatHistory": [],
                                        "modelId": "default"
                                    }
                                })
                            }
                        }
                        
                        message_json = json.dumps(message)
                        lsp_message = f"Content-Length: {len(message_json)}\r\n\r\n{message_json}"
                        
                        print(f"📤 发送LSP消息...")
                        sock.send(lsp_message.encode('utf-8'))
                        
                        # 等待响应
                        sock.settimeout(10)
                        response = sock.recv(4096)
                        print(f"📥 收到响应: {response.decode('utf-8')}")
                        
                        sock.close()
                        return True
                    else:
                        print(f"❌ 无法连接到端口 {port}")
                        
                except Exception as e:
                    print(f"❌ 端口 {port} 连接失败: {e}")
                finally:
                    try:
                        sock.close()
                    except:
                        pass
        
        # 方法2: 查找命名管道或Unix socket
        print("🔍 查找命名管道或Unix socket...")
        
        # 在Windows上查找命名管道
        if sys.platform == 'win32':
            import glob
            pipes = glob.glob(r'\\.\pipe\*augment*') + glob.glob(r'\\.\pipe\*sidecar*')
            if pipes:
                print(f"✅ 发现命名管道: {pipes}")
                # TODO: 实现命名管道通信
        
        return False
        
    except Exception as e:
        print(f"❌ 通信失败: {e}")
        return False

def inject_into_existing_process(pid):
    """尝试注入到现有进程"""
    print(f"💉 尝试注入到现有LSP进程 {pid}...")
    
    # 这种方法比较复杂，需要进程注入技术
    # 暂时跳过，因为可能需要特殊权限
    print("⚠️ 进程注入需要特殊权限，暂时跳过")
    return False

def main():
    print("🎯 连接现有Augment LSP进程")
    print("=" * 60)
    
    # 1. 查找现有LSP进程
    pid, cmdline = find_augment_lsp_process()
    
    if not pid:
        print("❌ 未找到现有的Augment LSP进程")
        print("请确保IntelliJ IDEA和Augment插件正在运行")
        return
    
    print(f"\n✅ 找到现有LSP进程: {pid}")
    
    # 2. 分析进程
    analyze_lsp_process(pid)
    
    # 3. 尝试通信
    print("\n" + "=" * 60)
    success = try_communicate_with_existing_process(pid)
    
    if success:
        print("\n🎉 成功与现有LSP进程通信！")
        print("请检查Augment聊天界面是否收到消息。")
    else:
        print("\n❌ 无法与现有LSP进程通信")
        print("\n💡 可能的原因：")
        print("1. LSP进程使用stdio通信，无法从外部访问")
        print("2. 需要特殊的认证或会话")
        print("3. 进程间通信被保护")
        
        # 4. 尝试其他方法
        print("\n🔧 尝试其他方法...")
        inject_into_existing_process(pid)

if __name__ == "__main__":
    main()
